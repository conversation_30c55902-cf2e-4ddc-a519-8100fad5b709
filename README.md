# Guepard - Database Version Control VS Code Extension

![<PERSON><PERSON><PERSON> Logo](public/assets/guepard-logo.png)

Guepard VS Plugin is a Visual Studio Code extension that brings Git-like version control capabilities to your databases, right inside the VS Code Editor.

## Table of Contents

- [Features](#features)
- [Installation](#installation)
- [Build](#build)
- [License](#license)

## Features

- **Branching Model**: Switch to different database versions effortlessly
- **Time Travel**: Restore previous states of the database.
- **Visual History**: View changes and database versions (snapshots) in an intuitive UI.
- **VS Code Integration**: Seamless interaction within the VS Code environment.

## Installation

```bash
code --install-extension guepard-0.0.1.vsix
```

### Set Environment Variables

Windows:
```cmd
setx API_TOKEN "your_token"
setx GUEPARD_API_BASE_URL "your_api_url"
setx SUPABASE_ANON_KEY "your_supabase_key"
setx GUEPARD_SUPABASE_API "your_supabase_api_url"
setx AUTH_EMAIL "your_email"
setx AUTH_PASSWORD "your_password"
```

macOS/Linux:
```bash
echo 'export API_TOKEN="your_token"' >> ~/.bashrc
echo 'export GUEPARD_API_BASE_URL="your_api_url"' >> ~/.bashrc
echo 'export SUPABASE_ANON_KEY="your_supabase_key"' >> ~/.bashrc
echo 'export GUEPARD_SUPABASE_API="your_supabase_api_url"' >> ~/.bashrc
echo 'export AUTH_EMAIL="your_email"' >> ~/.bashrc
echo 'export AUTH_PASSWORD="your_password"' >> ~/.bashrc
source ~/.bashrc
```

### Basic Commands

| Command                    | Description                                    |
|----------------------------|------------------------------------------------|
| **Guepard: Initialize**     | Start version control for the current database |
| **Guepard: Create Snapshot**| Record the current database state              |
| **Guepard: Create Branch**  | Create a new development branch                |
| **Guepard: Checkout Branch**  | Switch to a different database branch          |
| **Guepard: Checkout Bookmark**  | Switch to a different database version (snapshot)          |

### Prerequisites

Ensure you have the following installed:

- **Node.js 16+**
- **VS Code 1.70+**

### Build

To build the project, run the following commands:

```bash
pnpm install
pnpm run build
```

### 1.0.0

Initial release of Guepard VS Code Extension

### License

MIT © 2023 Guepard Team
