import esbuild from "esbuild";
import stylePlugin from "esbuild-style-plugin";
import path from "path";
import fs from "fs";
import postcss from "postcss";
import tailwindcss from "tailwindcss";

const stylePath = path.resolve(path.dirname(new URL(import.meta.url).pathname).replace(/^\/+/, ''), 'src/index.css');

/**
 * @type {import('esbuild').Plugin}
 */
const esbuildProblemMatcherPlugin = {
  name: 'esbuild-problem-matcher',

  setup(build) {
    build.onStart(() => {
      console.log('[watch] build started');
    });
    build.onEnd(result => {
      result.errors.forEach(({ text, location }) => {
        console.error(`✘ [ERROR] ${text}`);
        if (location === null) return;
        console.error(`    ${location.file}:${location.line}:${location.column}:`);
      });
      console.log('[watch] build finished');
    });
  }
};

// build CSS
async function buildCSS() {
  const css = fs.readFileSync(stylePath, 'utf8');
  const result = await postcss([
    tailwindcss,
  ]).process(css, {
    from: stylePath,
    to: 'dist/index.css'
  });

  fs.mkdirSync('dist', { recursive: true });
  fs.writeFileSync('dist/index.css', result.css);
}

const isProduction = process.argv.includes("--production");
const watchMode = process.argv.includes("--watch");

const baseConfig = {
  platform: 'node',
  bundle: true,
  minify: isProduction,
  sourcemap: !isProduction,
  metafile: true,
  external: ['vscode'],
  loader: {
    ".ts": "ts",
    ".tsx": "tsx",
    ".js": "js",
    ".jsx": "jsx",
    ".png": "dataurl",
    ".svg": "dataurl",
  },
  define: {
    'import.meta.env.DEV': isProduction ? 'false' : 'true',
  },
  plugins: [esbuildProblemMatcherPlugin, stylePlugin()],
};

// Main extension build config
const extensionBuild = {
  ...baseConfig,
  entryPoints: ["./src/extension.ts"],
  outdir: "dist",
  external: ["vscode"],
  format: "cjs",
  platform: "node",
};

// Webview Builds
const homeSidePanelWebviewBuild = {
  ...baseConfig,
  entryPoints: ["./src/ui/webviews/webview-sidepanel/panels/home/<USER>"],
  outfile: "./dist/side-panel-home-webview-provider.js",
  platform: "browser",
  format: "esm",
};

const welcomeSidePanelWebviewBuild = {
  ...baseConfig,
  entryPoints: ["./src/ui/webviews/webview-sidepanel/panels/welcome/index.tsx"],
  outfile: "./dist/side-panel-welcome-webview-provider.js",
  platform: "browser",
  format: "esm",
};

const mainPanelWebviewBuild = {
  ...baseConfig,
  entryPoints: ["./src/ui/webviews/webview-mainpanel/index.tsx"],
  outfile: "./dist/main-webview-provider.js",
  platform: "browser",
  format: "esm",
};

const build = async () => {
  try {
    await buildCSS();
    if (watchMode) {
      const extensionContext = await esbuild.context(extensionBuild);
      const homeSidePanelContext = await esbuild.context(homeSidePanelWebviewBuild);
      const welcomeSidePanelContext = await esbuild.context(welcomeSidePanelWebviewBuild);
      const mainPanelContext = await esbuild.context(mainPanelWebviewBuild);

      await Promise.all([
        extensionContext.watch(),
        homeSidePanelContext.watch(),
        welcomeSidePanelContext.watch(),
        mainPanelContext.watch(),
      ]);

      console.log("Watching for changes...");
    } else {
      await Promise.all([
        esbuild.build(extensionBuild),
        esbuild.build(homeSidePanelWebviewBuild),
        esbuild.build(welcomeSidePanelWebviewBuild),
        esbuild.build(mainPanelWebviewBuild),
      ]);

      console.log("Build complete");
    }
  } catch (err) {
    console.error("Build failed:", err);
    process.exit(1);
  }
};

build();
