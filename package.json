{"name": "gue<PERSON>", "displayName": "gue<PERSON>", "publisher": "<PERSON><PERSON><PERSON>", "description": "Guepard VS Code Extension", "repository": {"type": "git", "url": "https://github.com/Guepard-Corp/guepard-vcsplugin.git"}, "version": "0.0.1", "engines": {"vscode": "^1.97.0"}, "vsce": {"dependencies": false, "useYarn": false}, "activationEvents": ["onView:gue<PERSON>-home"], "main": "./dist/extension.js", "contributes": {"menus": {"webview/context": [{"command": "guepard.mainPanel.snapshotsTable.contextMenu.checkoutBookmark", "when": "webviewId == 'webviewMainPanel' && webviewSection == 'tableRow'"}, {"command": "guepard.mainPanel.snapshotsTable.contextMenu.checkoutBranch", "when": "webviewId == 'webviewMainPanel' && webviewSection == 'tableRowBadge'"}, {"command": "guepard.mainPanel.snapshotsTable.contextMenu.createBranch", "when": "webviewId == 'webviewMainPanel' && webviewSection == 'tableRow'"}], "view/item/context": [{"command": "authSettings.editSetting", "when": "view == authSettingsView", "group": "inline"}, {"command": "authSettings.checkSetting", "when": "view == authSettingsView", "group": "inline"}]}, "commands": [{"command": "guepard.showMainWebview", "title": "Show Main Webview"}, {"command": "guepard.refreshMainPanel", "title": "Refresh Main Window"}, {"command": "guepard.mainPanel.snapshotsTable.contextMenu.checkoutBranch", "title": "Checkout Branch", "category": "<PERSON><PERSON><PERSON>"}, {"command": "guepard.mainPanel.snapshotsTable.contextMenu.checkoutBookmark", "title": "Checkout Bookmark", "category": "<PERSON><PERSON><PERSON>"}, {"command": "guepard.mainPanel.snapshotsTable.contextMenu.createBranch", "title": "Create Branch", "category": "<PERSON><PERSON><PERSON>"}, {"command": "guepard.openDocs", "title": "Open Guepard Documentation"}, {"command": "authSettings.editSetting", "title": "Edit Setting"}, {"command": "authSettings.checkSetting", "title": "Check Registered Value"}], "viewsContainers": {"activitybar": [{"id": "guepard-explorer", "title": "<PERSON><PERSON><PERSON>", "icon": "public/assets/guepard-icon.png", "contextualTitle": "<PERSON><PERSON><PERSON>"}], "panel": [{"id": "guepardSidePanel", "title": "<PERSON><PERSON><PERSON>", "icon": "public/assets/guepard-icon.png"}]}, "viewsWelcome": [{"id": "guepard-welcome", "title": "Welcome"}, {"view": "gue<PERSON>-home", "contents": "No repositories found.\n[Create New Repository](command:guepard.selectRepository)"}], "views": {"guepard-explorer": [{"id": "guepard-welcome", "name": "Welcome to <PERSON><PERSON><PERSON>", "type": "webview"}, {"id": "gue<PERSON>-home", "name": "Home", "type": "webview"}, {"id": "guepard-add-bbokmark", "name": "Add Bookmark", "type": "webview"}, {"id": "guepard-database-graph", "name": "Database Graph", "type": "webview"}, {"id": "guepard-monitoring", "name": "Monitoring", "type": "webview"}, {"id": "guepard-logs", "name": "Logs", "type": "webview"}, {"id": "guepard-settings", "name": "Settings"}]}}, "scripts": {"vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint && node esbuild.js", "build": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "watch": "node esbuild.js --watch", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.66.1", "@types/mocha": "^10.0.10", "@types/node": "~20.17.18", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/vscode": "^1.97.0", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "@vscode-elements/webview-playground": "^1.6.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "autoprefixer": "^10.4.20", "esbuild": "^0.24.2", "esbuild-style-plugin": "^1.6.3", "eslint": "^9.19.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.2", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}, "dependencies": {"@gitgraph/core": "^1.5.0", "@gitgraph/react": "^1.6.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.48.1", "@tailwindcss/postcss": "^4.0.9", "@tanstack/react-query": "^5.66.8", "@tanstack/react-query-devtools": "4", "@tanstack/react-table": "^8.21.2", "@types/d3": "^7.4.3", "@vscode-elements/react-elements": "^0.9.0", "@vscode/codicons": "^0.0.36", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.1.2", "d3": "^7.9.0", "dotenv": "^16.4.7", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "mermaid": "^11.4.1", "module-alias": "^2.2.3", "next-themes": "^0.4.4", "radix-ui": "^1.1.3", "react": "^18.3.1", "react-dom": "18", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-table": "^7.8.0", "sonner": "^2.0.1", "svgo": "^3.3.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}}