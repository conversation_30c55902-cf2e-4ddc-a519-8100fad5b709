import * as vscode from 'vscode';
import { createClient, SupabaseClient, User, Session } from '@supabase/supabase-js';

export class AuthService {
    private supabase: SupabaseClient | null = null;
    private context: vscode.ExtensionContext;
    private readonly STORAGE_KEYS = {
        SESSION: 'supabase.session',
        SUPABASE_API: 'supabase.api.url',
        SUPABASE_API_KEY: 'supabase.api.key'
    };

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
    }

    private async loadEnvAndStoreSecrets(): Promise<void> {
        try {
            const supabaseUrl = process.env.SUPABASE_URL;
            const supabaseKey = process.env.SUPABASE_KEY;

            if (!supabaseUrl || !supabaseKey) {
                vscode.window.showErrorMessage('Missing SUPABASE_URL or SUPABASE_KEY in .env file.');
                return;
            }

            const storedUrl = await this.context.secrets.get(this.STORAGE_KEYS.SUPABASE_API);
            const storedKey = await this.context.secrets.get(this.STORAGE_KEYS.SUPABASE_API_KEY);

            if (!storedUrl || !storedKey) {
                await this.context.secrets.store(this.STORAGE_KEYS.SUPABASE_API, supabaseUrl);
                await this.context.secrets.store(this.STORAGE_KEYS.SUPABASE_API_KEY, supabaseKey);
                vscode.window.showInformationMessage('Supabase credentials set up successfully from .env file!');
            } else {
                vscode.window.showInformationMessage('Supabase credentials already set, skipping .env setup.');
            }
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to load .env file: ${error.message}`);
        }
    }

    async initialize() {
        try {

            await this.loadEnvAndStoreSecrets();

            const supabaseApi = await this.context.secrets.get(this.STORAGE_KEYS.SUPABASE_API);
            const supabaseApiKey = await this.context.secrets.get(this.STORAGE_KEYS.SUPABASE_API_KEY);

            if (!supabaseApi || !supabaseApiKey) {
                throw new Error('Missing Supabase credentials in secrets storage');
            }

            this.supabase = createClient(supabaseApi, supabaseApiKey);
            await this.restoreSession();
        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to initialize: ${error.message}`);
            throw error;
        }
    }

    getCommands(): vscode.Disposable[] {
        return [
            this.registerLoginCommand(),
            this.registerLogoutCommand()
        ];
    }

    private registerLoginCommand(): vscode.Disposable {
        return vscode.commands.registerCommand('guepard.login', async () => {
            try {
                if (!this.supabase) {
                    throw new Error('Guepard Supabase client not initialized');
                }

                const email = await vscode.window.showInputBox({
                    prompt: 'Enter your email',
                    placeHolder: '<EMAIL>',
                    ignoreFocusOut: true
                });

                if (!email) {return;}

                const password = await vscode.window.showInputBox({
                    prompt: 'Enter your password',
                    password: true,
                    ignoreFocusOut: true
                });

                if (!password) {return;}

                const { data, error } = await this.supabase.auth.signInWithPassword({
                    email,
                    password
                });

                if (error) {throw error;}

                await this.storeSession(data.session);
                vscode.window.showInformationMessage('Successfully logged in!');
                
            } catch (error: any) {
                vscode.window.showErrorMessage(`Login failed: ${error.message}`);
            }
        });
    }

    private registerLogoutCommand(): vscode.Disposable {
        return vscode.commands.registerCommand('guepard.logout', async () => {
            try {
                if (!this.supabase) {
                    throw new Error('Supabase client not initialized');
                }

                const { error } = await this.supabase.auth.signOut();
                if (error) {throw error;}

                await this.clearSession();
                vscode.window.showInformationMessage('Successfully logged out!');
                
            } catch (error: any) {
                vscode.window.showErrorMessage(`Logout failed: ${error.message}`);
            }
        });
    }

    private async storeSession(session: Session | null): Promise<void> {
        if (session) {
            await this.context.secrets.store(
                this.STORAGE_KEYS.SESSION,
                JSON.stringify(session)
            );
        }
    }

    private async clearSession(): Promise<void> {
        await this.context.secrets.delete(this.STORAGE_KEYS.SESSION);
    }

    private async restoreSession(): Promise<void> {
        try {
            const storedSession = await this.context.secrets.get(this.STORAGE_KEYS.SESSION);
            
            if (storedSession && this.supabase) {
                const session = JSON.parse(storedSession);
                const { data: { session: currentSession }, error } = 
                    await this.supabase.auth.setSession(session);
                
                if (error) {throw error;}

                if (currentSession) {
                    vscode.window.showInformationMessage('Session restored');
                }
            }
        } catch (error) {
            await this.clearSession();
        }
    }

    async getCurrentUser(): Promise<User | null> {
        try {
            if (!this.supabase) {
                throw new Error('Supabase client not initialized');
            }

            const { data: { user }, error } = await this.supabase.auth.getUser();
            if (error) {throw error;}
            return user;
        } catch (error) {
            return null;
        }
    }

    async isAuthenticated(): Promise<boolean> {
        try {
            const session = await this.context.secrets.get(this.STORAGE_KEYS.SESSION);
            return !!session;
        } catch {
            return false;
        }
    }

    async refreshSession(): Promise<Session | null> {
        try {
            if (!this.supabase) {
                throw new Error('Supabase client not initialized');
            }

            const { data: { session }, error } = await this.supabase.auth.refreshSession();
            if (error) {throw error;}
            
            await this.storeSession(session);
            return session;
        } catch (error) {
            return null;
        }
    }

    dispose() {
        this.supabase = null;
    }
}