import https from 'https';

// Define the SSL agent configuration
const sslAgent = new https.Agent({
  rejectUnauthorized: false, // Disable SSL verification (only for development)
});


export const config = {
  headers: {
    'Content-Type': 'application/json',
    'Expires': '0'
  },
  httpsAgent: sslAgent,
};

export const configWithToken = (accessToken: string) => (
  {

    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
      'Expires': '0',
    },
    httpsAgent: sslAgent,
  });

export const aiconfig = {
  headers: {
    'Content-Type': 'application/json'
  }
};