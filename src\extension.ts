import * as vscode from 'vscode';
import { StatusBarItem } from 'vscode';
import * as dotenv from 'dotenv';
import { EXTENSION_CONSTANT } from "./constant";
import { WebviewPanelProvider } from './ui/webviews/webview-mainpanel/provider/main-webview-provider';
import { createRepository, DeployDTO, getRepositories, getRepository } from './server/actions/repository-server-actions';
import { startCompute, statusCompute, stopCompute } from './server/actions/compute-server-actions';
import { Repository } from './ui/types/repository.type';
import { WelcomeSidePanelWebview } from './ui/webviews/webview-sidepanel/panels/welcome/provider/side-panel-welcome-webview-provider';
import { HomeSidePanelWebview } from './ui/webviews/webview-sidepanel/panels/home/<USER>/side-panel-home-webview-provider';
import { AuthSettingsProvider } from './ui/webviews/webview-sidepanel/panels/settings/provider/auth-settings-provider';

dotenv.config();

const updateStatusBarItem = (statusBarItem: vscode.StatusBarItem, repoData: Repository | undefined) => {
    if (repoData) {
        statusBarItem.text = `$(repo) ${repoData.repository_name}`;
        statusBarItem.tooltip = `Selected Repository: ${repoData.repository_name}`;
    } else {
        statusBarItem.text = "$(repo) Select Repository";
        statusBarItem.tooltip = "Click to select a repository";
    }
};

export async function activate(context: vscode.ExtensionContext) {
    try {

        context.globalState.update('selectedRepoData', null);

        //Temporary Authentication
        const apiToken = process.env.API_TOKEN;
        if (!apiToken) {
            throw new Error("Authentication failed. Check your .env settings.");
        }

        const authSettingsProvider = new AuthSettingsProvider(context);
        vscode.window.registerTreeDataProvider("guepard-settings", authSettingsProvider);

        context.subscriptions.push(
            vscode.commands.registerCommand("authSettings.editSetting", async (item) => {
                const newValue = await vscode.window.showInputBox({
                    prompt: `Enter new value for ${item.key}`,
                    value: item.value,
                });

                if (newValue) {
                    authSettingsProvider.updateSetting(item.key, newValue);
                }
            })
        );

        context.subscriptions.push(
            vscode.commands.registerCommand("authSettings.checkSetting", async (item) => {
                authSettingsProvider.checkRegisteredValue(item.key);
            })
        );
        console.info("Guepard extension activated.");

        //Status Bar
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
        statusBarItem.text = "$(repo) Select Repository";
        statusBarItem.tooltip = "Click to select a repository";
        statusBarItem.command = "guepard.selectRepository";
        statusBarItem.show();

        context.subscriptions.push(statusBarItem);

        //Main Panel
        const webviewPanelProvider = new WebviewPanelProvider(context.extensionUri, context);
        initializeMainWebview(context, webviewPanelProvider, statusBarItem);

        webviewPanelProvider.onStatusBarUpdate(() => {
            const db = context.globalState.get<Repository>('selectedRepoData');
            updateStatusBarItem(statusBarItem, db);
        });

        //Side Panel Views
        const welcomeSidePanelProvider = new WelcomeSidePanelWebview(context.extensionUri);
        context.subscriptions.push(
            vscode.window.registerWebviewViewProvider("guepard-welcome", welcomeSidePanelProvider)
        );

        const homeSidePanelProvider = new HomeSidePanelWebview(context.extensionUri, context);
        context.subscriptions.push(
            vscode.window.registerWebviewViewProvider("guepard-home", homeSidePanelProvider)
        );

    } catch (error) {
        vscode.window.showErrorMessage(`Failed to initialize extension: ${error}`);
        console.error("Extension activation error:", error);
    }
}

function initializeMainWebview(context: vscode.ExtensionContext, webviewPanelProvider: WebviewPanelProvider, statusBarItem: StatusBarItem) {
    const showMainWebviewCommand = vscode.commands.registerCommand('guepard.showMainWebview', () => {
        webviewPanelProvider.show();
    });
    context.subscriptions.push(showMainWebviewCommand);

    const refreshViewsCommand = vscode.commands.registerCommand('guepard.refreshViews', () => {
        webviewPanelProvider.refresh();
        vscode.window.showInformationMessage('Views refreshed successfully');
    });
    context.subscriptions.push(refreshViewsCommand);

    registerContextMenuCommands(context, webviewPanelProvider);
    registerComputeCommands(context, webviewPanelProvider);
    registerSelectDbCommands(context, webviewPanelProvider, statusBarItem);

    setTimeout(() => {
        webviewPanelProvider.show();
    }, 500);
}

//Context-menus commands (POST/PUT requests)
function registerContextMenuCommands(context: vscode.ExtensionContext, webviewPanelProvider: WebviewPanelProvider) {
    const contextMenuCreateBranch = vscode.commands.registerCommand('guepard.mainPanel.snapshotsTable.contextMenu.createBranch', () => {
        const webview = webviewPanelProvider.getWebview();
        if (webview) {
            webview.postMessage({ command: 'guepard.mainPanel.snapshotsTable.contextMenu.createBranch' });
        }
    });

    const contextMenuCheckoutBookmark = vscode.commands.registerCommand('guepard.mainPanel.snapshotsTable.contextMenu.checkoutBookmark', () => {
        const webview = webviewPanelProvider.getWebview();
        if (webview) {
            webview.postMessage({ command: 'guepard.mainPanel.snapshotsTable.contextMenu.checkoutBookmark' });
        }
    });

    const contextMenuCheckoutBranch = vscode.commands.registerCommand('guepard.mainPanel.snapshotsTable.contextMenu.checkoutBranch', () => {
        const webview = webviewPanelProvider.getWebview();
        if (webview) {
            webview.postMessage({ command: 'guepard.mainPanel.snapshotsTable.contextMenu.checkoutBranch' });
        }
    });

    context.subscriptions.push(
        contextMenuCreateBranch,
        contextMenuCheckoutBookmark,
        contextMenuCheckoutBranch
    );
}

// Status bar item
// Select Repository
function registerSelectDbCommands(context: vscode.ExtensionContext, webviewPanelProvider: WebviewPanelProvider, statusBarItem: StatusBarItem) {
    const initialRepoData = context.globalState.get<Repository>('selectedRepoData');
    updateStatusBarItem(statusBarItem, initialRepoData);

    const selectRepositoryCommand = vscode.commands.registerCommand("guepard.selectRepository", async () => {
        try {
            const repositories = await getRepositories();
            const items = [
                {
                    label: "Create New Repository...",
                    id: "newRepo",
                },
                ...repositories.map((repo: any) => ({
                    label: repo.repository_name,
                    id: repo.id,
                }))
            ];

            const selectedDatabase = await vscode.window.showQuickPick(items, {
                placeHolder: "Select a repository or add a new one",
            });

            const defaultValues = {
                database_provider: 'PostgreSQL',
                database_version: '16',
                region: 'us',
                instance_type: 'free',
                datacenter: 'us-west-aws',
                database_username: 'postgres',
                database_password: '',
                deployment_type: 'REPOSITORY',
            };

            if (selectedDatabase) {
                if (selectedDatabase.id === "newRepo") {
                    const repoName = await vscode.window.showInputBox({
                        prompt: "Enter the name for the new repository",
                    });

                    if (repoName) {
                        const deployData: DeployDTO = {
                            repository_name: repoName,
                            ...defaultValues
                        };

                        const newRepoData: Repository = await createRepository(deployData);
                        const depData = await getRepository(newRepoData.id);

                        vscode.window.showInformationMessage(`New repository "${depData.repository_name}" created.`);

                        updateStatusBarItem(statusBarItem, depData);
                        context.globalState.update('selectedRepoData', depData);

                        const webview = webviewPanelProvider.getWebview();
                        if (webview) {
                            webview.postMessage({
                                action: "LOAD_REPOSITORY_DATA",
                                data: {
                                    repositoryData: depData,
                                },
                            });
                        }

                        vscode.commands.executeCommand("guepard.refreshComputeStatus");
                    } else {
                        vscode.window.showErrorMessage("Repository name cannot be empty.");
                    }
                } else {
                    vscode.window.showInformationMessage(`Selected repository: ${selectedDatabase.label}`);

                    const repoData = await getRepository(selectedDatabase.id);

                    updateStatusBarItem(statusBarItem, repoData);
                    context.globalState.update('selectedRepoData', repoData);

                    const webview = webviewPanelProvider.getWebview();
                    if (webview) {
                        webview.postMessage({
                            action: "LOAD_REPOSITORY_DATA",
                            data: {
                                repositoryData: repoData,
                            },
                        });
                    }

                    vscode.commands.executeCommand("guepard.refreshComputeStatus");
                }
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to fetch repositories in selectRepositoryCommand: ${error instanceof Error ? error.message : String(error)}`);
        }
    });

    context.subscriptions.push(selectRepositoryCommand);

    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(() => {
            const db = context.globalState.get<Repository>('selectedRepoData');
            updateStatusBarItem(statusBarItem, db);
            vscode.commands.executeCommand("guepard.refreshComputeStatus");
        })
    );
}

// Status bar item
// Compute
function registerComputeCommands(context: vscode.ExtensionContext, webviewPanelProvider: WebviewPanelProvider) {
    const computeStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 90);
    computeStatusBarItem.text = "$(circle-outline) Compute Inactive";
    computeStatusBarItem.tooltip = "Click to start computation";
    computeStatusBarItem.command = "guepard.startCompute";
    computeStatusBarItem.show();

    context.subscriptions.push(computeStatusBarItem);

    const updateComputeStatus = async () => {
        try {
            const repoData = context.globalState.get<Repository>('selectedRepoData');
            if (!repoData) {
                computeStatusBarItem.text = "$(circle-outline) No Compute ";
                computeStatusBarItem.tooltip = "No repository selected. Please select a repository first.";
                computeStatusBarItem.command = undefined;
                computeStatusBarItem.backgroundColor = undefined;
                return;
            }

            const repoDetails = await getRepository(repoData.id);
            const statusResult = await statusCompute(repoDetails.id, repoDetails.clone_id);

            if (statusResult.success && statusResult.status === 200) {
                computeStatusBarItem.text = `$(circle-filled) ${repoDetails.name}`;
                computeStatusBarItem.tooltip = "Compute is active - Click to stop";
                computeStatusBarItem.command = "guepard.stopCompute";
                computeStatusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
            } else if (!statusResult.success && statusResult.status === 410 &&
                statusResult.data?.message === "Database is not healthy") {
                computeStatusBarItem.text = "$(loading~spin) Compute Deploying";
                computeStatusBarItem.tooltip = "Compute is deploying";
                computeStatusBarItem.command = undefined;
                computeStatusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
            } else {
                computeStatusBarItem.text = `$(circle-outline) ${repoDetails.name}`;
                computeStatusBarItem.tooltip = "Click to start computation";
                computeStatusBarItem.command = "guepard.startCompute";
                computeStatusBarItem.backgroundColor = undefined;
            }
        } catch (error) {
            console.error("Error checking compute status:", error);
            computeStatusBarItem.text = "$(circle-outline) Compute Inactive";
            computeStatusBarItem.tooltip = "Click to start computation";
            computeStatusBarItem.command = "guepard.startCompute";
            computeStatusBarItem.backgroundColor = undefined;
        }
    };

    const statusInterval = setInterval(updateComputeStatus, 30000);
    updateComputeStatus();

    context.subscriptions.push({ dispose: () => clearInterval(statusInterval) });

    const startComputeCommand = vscode.commands.registerCommand("guepard.startCompute", async () => {
        try {
            const repoData = context.globalState.get<Repository>('selectedRepoData');

            if (!repoData) {
                vscode.window.showErrorMessage("No repository selected. Please select a repository first.");
                return;
            }

            const repoDetails = await getRepository(repoData.id);
            console.log("Global State Repository Data:", repoDetails);

            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "Starting Compute",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Initializing..." });
                computeStatusBarItem.text = `$(loading~spin) Starting Compute: ${repoDetails.name}`;
                computeStatusBarItem.tooltip = "Starting computation...";
                computeStatusBarItem.command = undefined;
                computeStatusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');

                progress.report({ increment: 50, message: "Contacting server..." });

                const result = await startCompute(repoDetails.id, repoDetails.clone_id);

                progress.report({ increment: 50, message: "Finalizing..." });

                if (result.success) {
                    updateComputeStatus();
                    vscode.window.showInformationMessage(`Compute ${repoDetails.name} started successfully`);
                } else {
                    vscode.window.showErrorMessage("Failed to start computation.");
                    computeStatusBarItem.text = "$(circle-outline) Compute Inactive";
                    computeStatusBarItem.tooltip = "Click to start computation";
                    computeStatusBarItem.command = "guepard.startCompute";
                    computeStatusBarItem.backgroundColor = undefined;
                }
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to start computation: ${error instanceof Error ? error.message : String(error)}`);
            console.error("Error during startCompute:", error);
            computeStatusBarItem.text = "$(circle-outline) Compute Inactive";
            computeStatusBarItem.tooltip = "Click to start computation";
            computeStatusBarItem.command = "guepard.startCompute";
            computeStatusBarItem.backgroundColor = undefined;
        }
    });

    const stopComputeCommand = vscode.commands.registerCommand("guepard.stopCompute", async () => {
        try {
            const repoData = context.globalState.get<Repository>('selectedRepoData');

            if (!repoData) {
                vscode.window.showErrorMessage("No repository selected. Please select a repository first.");
                return;
            }

            const repoDetails = await getRepository(repoData.id);
            console.log("Global State Repository Data:", repoDetails);

            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "Stopping Compute",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Initializing..." });

                computeStatusBarItem.text = "$(loading~spin) Stopping Compute";
                computeStatusBarItem.tooltip = "Stopping computation...";
                computeStatusBarItem.command = undefined;
                computeStatusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');

                progress.report({ increment: 50, message: "Contacting server..." });

                const result = await stopCompute(repoDetails.id, repoDetails.clone_id);

                progress.report({ increment: 50, message: "Finalizing..." });

                if (result.success) {
                    updateComputeStatus();
                    vscode.window.showInformationMessage(`Compute ${repoDetails.name} stopped successfully`);
                } else {
                    vscode.window.showErrorMessage("Failed to stop computation.");
                    updateComputeStatus();
                }
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to stop computation: ${error instanceof Error ? error.message : String(error)}`);
            console.error("Error during stopCompute:", error);
            updateComputeStatus();
        }
    });

    const refreshStatusCommand = vscode.commands.registerCommand("guepard.refreshComputeStatus", updateComputeStatus);

    context.subscriptions.push(startComputeCommand, stopComputeCommand, refreshStatusCommand);
}

export function deactivate(context: vscode.ExtensionContext) {
    context.globalState.update('selectedRepoData', null);
    console.info("Guepard extension deactivated.");
}