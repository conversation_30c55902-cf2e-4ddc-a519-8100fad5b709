@tailwind base;
@tailwind components;
@tailwind utilities;

/* ############################### */
/* ### Snapshot List Component ### */
/* ############################### */

body {
  padding: 0;
}

#commits-outside-container {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  /* height: 100vh; Ensure full viewport height */
  /* overflow-y: hidden; Scroll if the graph grows vertically */
  z-index: 0;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(87, 85, 67, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.snapshot-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-weight: bold;
  font-size: 0.6em;
  position: relative;
  margin-right: 10px;
}

.snapshot-circle:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

.status-icon {
  color: var(--completed-color);
  margin-left: 5px;
}

.status-icon.incomplete {
  color: var(--incomplete-color);
}

/* ################################ */
/* ### Snapshot Graph Component ### */
/* ################################ */
.instant-deploy-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.custom-dot {
  color: #118ab2;
}

.centered {
  justify-content: center;
  text-align: center;
}

.instant-deploy-container .status-container {
  align-items: center;
  margin-bottom: -8px;
}

.graph-container {
  position: relative;
}

.chart-container {
  width: 100%;
  padding: 20px;
  background-color: var(--chart-background-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-right: 10px;
}

.tooltip {
  visibility: hidden;
  background-color: var(--text-color);
  color: var(--background-color);
  text-align: center;
  border-radius: 6px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1000;
  bottom: 175%;
  /* Position the tooltip above the text */
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  white-space: nowrap;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  /* Bottom of the tooltip */
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: var(--text-color) transparent transparent transparent;
  z-index: 1000;
}

#hoverCardContainer {
  position: absolute;
  background: white;
  border: 1px solid #ccc;
  padding: 10px;
  display: none;
  z-index: 10;
  /* Ensure hover card is above other elements */
}

#hoverCardContainer #commit-time-message {
  font-size: 14px;
  color: black;
}

.snapshot-graph-container {
  /*overflow: visible;
   z-index: 10;
     overflow-x: auto; Enable horizontal scrolling */
  /* max-width: calc(30 * 20px + 60px + 30px); Max width based on max branches and margins */
}

.branchLine {
  transition: all 0.3s ease;
}

.commitDot {
  transition: all 0.3s ease;
}

.added {
  border-left: 3px solid green;
  padding-left: 5px;
}

.updated {
  border-left: 3px solid blue;
  padding-left: 5px;
}

.deleted {
  border-left: 3px solid red;
  padding-left: 5px;
}

/* Buttons hint tooltip */
.button-tooltip {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-weight: bold;
  font-size: 0.6em;
  position: relative;
  transition: opacity 0.3s;
  margin-right: 10px;
  z-index: 1000;
  /* Ensure tooltip is on top */
}

.button-tooltip:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

.guepard-logo {
  width: 180px;
  /* Adjust the width as needed */
  margin-bottom: 50px;
  /* Add some space below the logo */
}

/*LogsTab CSS*/
.custom-textarea textarea {
  /* Custom Scrollbar styles */
  scrollbar-width: thin;
  scrollbar-color: rgb(92, 89, 89) var(--background-color);
  /* Color of the scrollbar thumb and track */
}

/*Deployment activity text*/
.status-message-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sub-header-container {
  display: flex;
  align-items: center;
}

.settings-botton {
  margin-left: 500px;
  /* Push the button to the right */
}

.left-header {
  margin: 0;
  margin-right: 570px;
}

.center-header {
  margin: 0;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.header-divider {
  margin-top: 80px;
  width: 100%;
}

.settings-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  /* Adjust as needed for spacing between text and toggle */
}

.settings-item p {
  margin: 0;
  /* Removes default margin from paragraph */
  font-size: 1em;
  white-space: nowrap;
}

/* You can add these classes in your global or component-specific CSS file */
.tree-root {
  list-style: none;
  padding-left: 0;
}

.tree-root {
  list-style-type: none;
  padding-left: 0;
}

.tree-folder-name,
.tree-file-name {
  cursor: pointer;
  border-radius: 0.25rem;
  transition: background-color 0.3s ease;
}

.tree-folder-name:hover,
.tree-file-name:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.tree-file-name {
  margin-left: 1rem;
}

progress {
  height: 10px;
  border-radius: 5px;
}

progress::-webkit-progress-bar {
  background-color: #f3f3f3;
  border-radius: 5px;
}

progress::-webkit-progress-value {
  background-color: #f3ba00;
  border-radius: 5px;
}

/* Memory Progress Bar */
.progress-cpu::-webkit-progress-bar {
  background-color: #e0f7fa;
  /* Background color for memory bar */
  border-radius: 5px;
}

.progress-cpu::-webkit-progress-value {
  background-color: #097969;
  /* Fill color for memory bar */
  border-radius: 5px;
}

/* CPU Progress Bar */
.progress-memory::-webkit-progress-bar {
  background-color: #fbe9e7;
  /* Background color for CPU bar */
  border-radius: 5px;
}

.progress-memory::-webkit-progress-value {
  background-color: #f13737;
  /* Fill color for CPU bar */
  border-radius: 5px;
}

/* CPU Progress Bar */
.progress-memory-dark::-webkit-progress-bar {
  background-color: #c6d5fc;
  /* Background color for CPU bar */
  border-radius: 5px;
}

.progress-memory-dark::-webkit-progress-value {
  background-color: #3647ff;
  /* Fill color for CPU bar */
  border-radius: 5px;
}