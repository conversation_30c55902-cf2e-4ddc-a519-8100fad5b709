import fs from "fs";
import path from "path";
import axios from "axios";
import { execSync } from "child_process";
import * as dotenv from "dotenv";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const ENV_FILE_PATH = path.join(__dirname, "../../.env");

console.log("📂 Loading environment variables...");
dotenv.config({ path: ENV_FILE_PATH });

// Required environment variables
const REQUIRED_ENV_VARS = [
  "GUEPARD_SUPABASE_API",
  "AUTH_EMAIL",
  "AUTH_PASSWORD",
  "SUPABASE_ANON_KEY",
  "GUEPARD_API_BASE_URL",
  "API_TOKEN",
];

export function getEnvVar(key) {
  return process.env[key];
}

function updateEnvFile(key, value) {
  console.log(`📝 Updating .env file: ${key}=${value}`);

  let envContent = fs.existsSync(ENV_FILE_PATH) ? fs.readFileSync(ENV_FILE_PATH, "utf8") : "";

  const envLines = envContent.split("\n").filter(line => !line.startsWith(`${key}=`));
  envLines.push(`${key}=${value}`);

  fs.writeFileSync(ENV_FILE_PATH, envLines.join("\n"), "utf8");
  console.log(`✅ ${key} updated in .env file.`);

  // Reload environment variables
  dotenv.config({ path: ENV_FILE_PATH });
}

async function fetchToken() {
  try {
    console.log("🔑 Fetching new API token...");

    const apiUrl = getEnvVar("GUEPARD_SUPABASE_API");
    if (!apiUrl) throw new Error("❌ GUEPARD_SUPABASE_API is not set in .env");

    const authUrl = `${apiUrl}/auth/v1/token?grant_type=password`;
    console.log(`📡 Sending authentication request to: ${authUrl}`);

    const requestBody = {
      email: getEnvVar("AUTH_EMAIL"),
      password: getEnvVar("AUTH_PASSWORD"),
    };

    const response = await axios.post(authUrl, requestBody, {
      headers: {
        "Content-Type": "application/json",
        "apikey": getEnvVar("SUPABASE_ANON_KEY"),
      },
    });

    if (!response.data || !response.data.access_token) {
      throw new Error("❌ No token received from auth API");
    }

    const newToken = response.data.access_token;
    console.log("✅ New token received:", newToken);

    return newToken;
  } catch (error) {
    console.error("❌ Failed to fetch token:");
    return null;
  }
}

async function testNewToken(newToken) {
  try {
    console.log("🧪 Testing new token...");

    const apiUrl = getEnvVar("GUEPARD_API_BASE_URL");
    if (!apiUrl) throw new Error("❌ GUEPARD_API_BASE_URL is not set in .env");

    const testUrl = `${apiUrl}/test`; 
    console.log(`📡 Sending test request to: ${testUrl}`);

    const response = await axios.get(testUrl, {
      headers: { Authorization: `Bearer ${newToken}` },
    });

    if (response.status === 200) {
      console.log("✅ New token is valid.");
      return true;
    }

    console.error(`❌ New token test failed with status`);
    return false;
  } catch (error) {
    console.error("❌ Error testing new token:");
    return false;
  }
}

async function checkApiStatus() {

  console.log("🔍 Checking API status...");

  const apiUrl = getEnvVar("GUEPARD_API_BASE_URL");
  if (!apiUrl) throw new Error("❌ GUEPARD_API_BASE_URL is not set in .env");

  let token = getEnvVar("API_TOKEN");
  console.log(`📡 Sending request to: ${apiUrl}/deploy`);

  let response = await axios.get(`${apiUrl}/deploy`, {
    headers: { Authorization: `Bearer ${token}` },
  });

  if (response.status === 200) {
    console.log("✅ API is accessible with the current token.");
    return true;
  }

  if (response.status === 403) {
    console.log("❌ API returned 403 Forbidden. Fetching a new token...");
    const newToken = await fetchToken();

    if (newToken) {
      const isValid = await testNewToken(newToken);

      if (isValid) {
        console.log("✅ Replacing old token with new token.");
        updateEnvFile("API_TOKEN", newToken);
        try {
          console.log("🔄 Updating system environment variable...");
          execSync(`setx API_TOKEN ${newToken}`);
          console.log("✅ System environment updated with new API_TOKEN.");
        } catch (error) {
          console.error("❌ Failed to update system environment:");
        }

        terminateVSCode();
        return false;
      } else {
        console.log("❌ New token is invalid. Keeping old token.");
        return false;
      }
    }
  }
  console.log(`❌ Unexpected API response: ${response.status}`);
  return false;

}

function terminateVSCode() {
  console.log("⚠️ Preparing to terminate VS Code...");

  let countdown = 10;
  const interval = setInterval(() => {
    console.log(`⏳ VS Code will close in ${countdown} seconds...`);
    countdown--;
    if (countdown <= 0) {
      clearInterval(interval);
      try {
        console.log("💀 Attempting to terminate VS Code...");

        const isWindows = process.platform === "win32";
        const killCommand = isWindows ? "taskkill /F /IM Code.exe" : "pkill -f 'Visual Studio Code'";
        execSync(killCommand);

        console.log("✅ VS Code terminated successfully.");
      } catch (error) {
        console.error("❌ Failed to terminate VS Code:", error.message);
      }
    }
  }, 1000);
}

async function main() {
  console.log("🚀 Starting script...");
  const isApiAccessible = await checkApiStatus();
  if (!isApiAccessible) {
    console.log("🔄 Process completed.");
  }
}
/* main(); */