
import { Bookmark, Bookmarks, CreateBookmarkRequest } from '@/ui/types/bookmark.type';
import axios from 'axios';
import { CreateBranchRequest } from '@/ui/types/branch.type';
import { configWithToken } from '@/config/config';
import { requireAccessTokenComponent } from '../require-access-token-component';

const apiUrl: string = process.env.GUEPARD_API_BASE_URL;

export async function getAllSnapshots(deploymentId: string): Promise<Bookmarks> {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  try {
    const response = await axios.get(`${apiUrl}/deploy/${deploymentId}/snap`, {
      ...configWithToken(accessToken),
    });
    return response.data;
  } catch (error) {
    console.error('Error getting all snapshots', error);
    throw error;
  }
}

export async function getSnapshots(deploymentId: string, cloneId: string): Promise<Bookmarks> {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  try {
    const response = await axios.get(`${apiUrl}/deploy/${deploymentId}/${cloneId}/snap`, {
      ...configWithToken(accessToken),
    });
    return response.data;
  } catch (error) {
    console.error('Error getting branch snapshots', error);
    throw error;
  }
}

export const createBookmark = async (createBookmarkRequest: CreateBookmarkRequest) => {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  const response = await axios.post(`${apiUrl}/deploy/${createBookmarkRequest.repositoryId}/${createBookmarkRequest.branchId}/snap`,
    { snapshot_comment: createBookmarkRequest.snapshot_comment }, {
    ...configWithToken(accessToken),
  });
  if (!response.data) {
    return { success: false, error: "Failed to create a bookmark" };
  }
  return response.data;
};


export const checkoutBookmark = async (createBranchRequest: CreateBranchRequest) => {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  const response = await axios.post(`${apiUrl}/deploy/${createBranchRequest.repositoryId}/${createBranchRequest.branchId}/${createBranchRequest.bookmarkId}/branch`, createBranchRequest, {
    ...configWithToken(accessToken),
  });
  if (!response.data) {
    return { success: false, error: "Failed to checkout bookmark" };
  }
  return response.data;
};