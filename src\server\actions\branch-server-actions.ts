
import axios from 'axios';
import { Branch, Branches, CheckoutBranchRequest, CreateBranchRequest } from '@/ui/types/branch.type';
import { requireAccessTokenComponent } from '../require-access-token-component';
import { configWithToken } from '@/config/config';

const apiUrl: string = process.env.GUEPARD_API_BASE_URL;

export async function getBranches(deploymentId: string): Promise<Branches> {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  try {
    const response = await axios.get(`${apiUrl}/deploy/${deploymentId}/clone`, {
      ...configWithToken(accessToken),
    });

    return response.data;

  } catch (error) {
    console.error('Error getting branches', error);
    throw error;
  }
};

export async function getBranch(deploymentId: string, cloneId: string) {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  try {
    const response = await axios.get(`${apiUrl}/deploy/${deploymentId}/${cloneId}`, {
      ...configWithToken(accessToken),
    });

    return {
      data: response.data,
      error: undefined,
    };

  } catch (error) {
    console.error('Error getting branch', error);
    throw error;
  }
};

export const createBranch = async (createBranchRequest: CreateBranchRequest) => {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  const response = await axios.post(`${apiUrl}/deploy/${createBranchRequest.repositoryId}/${createBranchRequest.branchId}/${createBranchRequest.bookmarkId}/branch`,
    {
      branch_name: createBranchRequest.branch_name,
      discard_changes: createBranchRequest.discard_changes,
      checkout: createBranchRequest.checkout,
      ephemeral: createBranchRequest.ephemeral
    }, {
    ...configWithToken(accessToken),
  });
  if (!response.data) {
    return { success: false, error: "Failed to create a branch" };
  }
  return {
    data: response.data,
    success: true,
  };
};


export const checkoutBranch = async (checkoutBranchRequest: CheckoutBranchRequest) => {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  const response = await axios.post(`${apiUrl}/deploy/${checkoutBranchRequest.repositoryId}/${checkoutBranchRequest.branchId}/checkout`, null, {
    ...configWithToken(accessToken),
  });
  if (!response.data) {
    return { success: false, error: "Failed to checkout bookmark" };
  }
  return response.data;

};

export const updateEphemeralClone = async (deploymentId: string, cloneId: string) => {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  try {
    const response = await axios.put(`${apiUrl}/deploy/${deploymentId}/${cloneId}`, null, {
      ...configWithToken(accessToken),
    });
    return response;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      return { status: error.response.status, data: error.response.data };
    }
    throw error;
  }
};

export async function updateBranch(data: Partial<Branch>): Promise<any> {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  try {
    const response = await axios.put(`${apiUrl}/deploy/${data.deployment_id}/${data.id}/branch`, data, {
      ...configWithToken(accessToken),
    });
    return { success: true, data: response.data };
  } catch (error) {
    return { success: false, error: " Failed to update Branch" };
  }
};