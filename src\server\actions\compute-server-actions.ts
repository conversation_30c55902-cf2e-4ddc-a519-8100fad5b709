import axios from 'axios';
import { requireAccessTokenComponent } from '../require-access-token-component';
import { configWithToken } from '@/config/config';
import { Compute } from '@/ui/types/compute.type';
import { getRepository } from './repository-server-actions';

const apiUrl: string = process.env.GUEPARD_API_BASE_URL;

type SingleQueryResponse = {
    data: Compute | null;
    error?: string;
};

type LogResponse = {
    success: boolean;
    data: {
        stdout_logs: string,
        stderr_logs: string
    } | null;
    error?: string;
};

/**
 * Fetch logs for a specific repository and compute.
 * @param repositoryId - The ID of the repository.
 * @param computeId - The ID of the compute.
 * @returns Logs or an error message.
 */
export const getLogs = async (repositoryId: string, computeId: string): Promise<LogResponse> => {
    const { accessToken } = await requireAccessTokenComponent();

    if (!accessToken) {
        throw new Error('Access token is missing or invalid');
    }

    try {
        const response = await axios.get(`${apiUrl}/deploy/${repositoryId}/${computeId}/logs`, {
            ...configWithToken(accessToken),
        });
        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        // console.error('Error fetching logs:', error);

        if (axios.isAxiosError(error) && error.response) {
            return {
                success: false,
                data: null,
                error: error.response.data?.message || 'An error occurred while fetching logs',
            };
        }

        throw error;
    }
};

/**
 * Get the status of a compute instance.
 */
export const statusCompute = async (repositoryId: string, computeId: string): Promise<{ success: boolean; status: number; data: any }> => {
    const { accessToken } = await requireAccessTokenComponent();

    if (!accessToken) {
        throw new Error('Access token is missing or invalid');
    }

    try {
        const response = await axios.get(`${apiUrl}/deploy/${repositoryId}/${computeId}/status`, {
            ...configWithToken(accessToken),
        });
        return { success: true, status: response.status, data: response.data };
    } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
            return { success: false, status: error.response.status, data: error.response.data };
        }
        throw error;
    }
};

/**
* Start a compute instance.
*/
export const startCompute = async (repositoryId: string, computeId: string): Promise<{ success: boolean; error?: string }> => {
    const { accessToken } = await requireAccessTokenComponent();

    if (!accessToken) {
        throw new Error('Access token is missing or invalid');
    }

    try {
        await axios.get(`${apiUrl}/deploy/${repositoryId}/${computeId}/start`, {
            ...configWithToken(accessToken),
        });
        return { success: true };
    } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
            return {
                success: false,
                error: error.response.data?.message || 'An error occurred while starting compute'
            };
        }
        throw error;
    }
};

/**
* Stop a compute instance.
*/
export const stopCompute = async (repositoryId: string, computeId: string): Promise<{ success: boolean; error?: string }> => {
    const { accessToken } = await requireAccessTokenComponent();

    if (!accessToken) {
        throw new Error('Access token is missing or invalid');
    }

    try {
        await axios.get(`${apiUrl}/deploy/${repositoryId}/${computeId}/stop`, {
            ...configWithToken(accessToken),
        });
        return { success: true };
    } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
            return {
                success: false,
                error: error.response.data?.message || 'An error occurred while stopping compute'
            };
        }
        throw error;
    }
};

/**
* Monitor a database instance.
*/
export const metricsCompute = async (repositoryId: string, computeId: string): Promise<any> => {
    const { accessToken } = await requireAccessTokenComponent();

    if (!accessToken) {
        throw new Error('Access token is missing or invalid');
    }

    try {
        const response = await axios.get(`${apiUrl}/deploy/${repositoryId}/${computeId}/metrics`, {
            ...configWithToken(accessToken),
        });
        return {
            success: true,
            data: response.data,
        }; // This will contain the AllocationStats data
    } catch (error) {
        // console.error('Error fetching monitoring stats:', error);
        if (axios.isAxiosError(error) && error.response) {
            return {
                success: false,
                data: null,
                error: error.response.data?.message || 'An error occurred while fetching logs',
            };
        }
        throw error;
    }
};


export async function getCompute(repositoryId: string, cloneId: string): Promise<Compute> {
    const { accessToken } = await requireAccessTokenComponent();
    if (!accessToken) {
        throw new Error("Access token is missing or invalid");
    }
    const url = `${apiUrl}/deploy/${repositoryId}/${cloneId}`;
    try {
        const response = await axios.get(url, {
            ...configWithToken(accessToken),
        });

        return response.data;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        }
        throw new Error("Error fetching compute");
    }
}