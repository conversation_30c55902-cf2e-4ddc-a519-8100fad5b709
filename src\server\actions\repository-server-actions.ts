import axios from 'axios';
import https from 'https';
import { requireAccessTokenComponent } from '../require-access-token-component';

import { configWithToken } from '@/config/config';
import { Repositories, Repository } from '@/ui/types/repository.type';

export interface DeployDTO {
  repository_name: string;
  database_provider: string;
  database_version: string;
  region: string;
  datacenter: string;
  instance_type: string;
  deployment_type: string;
  performance_profile_id?: string;
}

const sslAgent = new https.Agent({
  rejectUnauthorized: false,
});

const apiUrl: string = process.env.GUEPARD_API_BASE_URL;

export async function getRepositories(): Promise<Repositories> {
  const { accessToken } = await requireAccessTokenComponent();

  const response = await axios.get(`${apiUrl}/deploy`, {
    ...configWithToken(accessToken || ''),
  });
  if (!response.data) {
    return [];
  }
  return response.data;
};

export async function getRepository(deploymentId: string): Promise<Repository> {
  const { accessToken } = await requireAccessTokenComponent();
  if (!accessToken) {
    throw new Error("Access token is missing or invalid");
  }
  try {
    const response = await axios.get(`${apiUrl}/deploy/${deploymentId}`, {
      ...configWithToken(accessToken),
    });
    return response.data;
  } catch (error) {
    console.error('Error getting deployment', error);
    throw error;
  }
};

export const createRepository =
  async (deployDTO: DeployDTO) => {
    const { accessToken } = await requireAccessTokenComponent();

    try {
      const response = await axios.post(`${apiUrl}/deploy`, deployDTO, {
        ...configWithToken(accessToken || ''),
        httpsAgent: sslAgent,
      });

      if (!response.data) {
        throw new Error("Failed to deploy repository");
      }
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 402) {
        throw new Error("plan exceeded");
      } else {
        throw new Error("Failed to deploy repository");
      }
    }
  };

export async function updateRepository(id: string, data: Partial<Repository>): Promise<Repository> {
  const { accessToken } = await requireAccessTokenComponent();
  try {
    const response = await axios.put(`${apiUrl}/deploy/${id}`, {
      repository_name: data.repository_name
    }, {
      ...configWithToken(accessToken || ''),
      httpsAgent: sslAgent,
    });
    return response.data;
  } catch (error) {
    throw new Error("Failed to update repository");
  }
}