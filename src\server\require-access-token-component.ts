
/* import { getSupabaseServerClient } from '@kit/supabase/server-client'; */

export const requireAccessTokenComponent = async () => {

  /* const client = await getSupabaseServerClient();
  const result = await requireUser(client); 

  if (result.error) {
    redirect(result.redirectTo);
  }
  

  // Extract the access token from the Supabase session
  const { data: session } = await client.auth.getSession();
  const accessToken = session.session?.access_token;
  */
  const accessToken: string = process.env.API_TOKEN;
  
  return {
    accessToken,
  };
};
