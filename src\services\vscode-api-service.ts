export interface VSCodeAPI {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
}

class VSCodeService {
  private static instance: VSCodeService | null = null;
  private vscodeApi: VSCodeAPI;
  private messageListener: (event: MessageEvent) => void;

  private constructor() {
    this.vscodeApi = acquireVsCodeApi();
    this.messageListener = (event) => this.handleMessage(event);
    window.addEventListener('message', this.messageListener);
  }

  public static getInstance(): VSCodeService {
    if (!VSCodeService.instance) {
      VSCodeService.instance = new VSCodeService();
    }
    return VSCodeService.instance;
  }

  public postMessage(message: any): void {
    this.vscodeApi.postMessage(message);
  }

  public getState(): any {
    return this.vscodeApi.getState();
  }

  public setState(state: any): void {
    this.vscodeApi.setState(state);
  }

  public onMessage(callback: (message: any) => void): void {
    window.addEventListener('message', (event) => {
      callback(event.data);
    });
  }

  public removeMessageListener(callback: (message: any) => void): void {
    window.removeEventListener('message', callback as EventListener);
  }

  public onDidChangeConfiguration(callback: (config: any) => void): { dispose: () => void } {
    const listener = (message: any) => {
      if (message.type === 'configChange') {
        callback(message.config);
      }
    };

    this.onMessage(listener);

    return {
      dispose: () => this.removeMessageListener(listener),
    };
  }


  private handleMessage(event: MessageEvent): void {

  }

  public dispose(): void {
    window.removeEventListener('message', this.messageListener);
    VSCodeService.instance = null;
  }
}

declare global {
  function acquireVsCodeApi(): VSCodeAPI;
}

export default VSCodeService;