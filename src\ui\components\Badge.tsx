import React from "react";
import { GitBranch, Loader2, LaptopMinimal } from "lucide-react";

interface BadgeProps {
    children: React.ReactNode;
    color?: string;
    bgColor?: string;
    borderColor?: string;
    width?: string;
    textSize?: string;
    style?: React.CSSProperties;
    height?: string;
    isLoading?: boolean;
    icon: string;
}

const iconMap: Record<string, JSX.Element> = {
    gitBranch: <GitBranch size={14} />,
    loader: <Loader2 className="mr-2 h-4 w-4 animate-spin" />,
    laptop: <LaptopMinimal size={16} />,
};

const Badge: React.FC<BadgeProps> = ({
    children,
    color = "#ccc",
    borderColor = color,
    bgColor = "transparent",
    width = "100%",
    textSize = "14px",
    style,
    height = "20px",
    isLoading = false,
    icon
}) => {
    return (
        <div
            className="gap-2 p-2 rounded-md border"
            style={{
                display: "flex",
                alignItems: "center",
                borderColor: borderColor,
                color: color,
                backgroundColor: bgColor,
                height: height,
                width: width,
                fontSize: textSize,
                ...style,
            }}
        >
            <span style={{ color: color }}>
                {isLoading ? iconMap.loader : iconMap[icon] || iconMap.gitBranch}
            </span>
            <span className="font-medium" style={{ fontSize: textSize }}>{children}</span>
        </div>
    );
};

export default Badge;
