import React, { useEffect, useRef } from "react";
import * as d3 from "d3";
import { drawBorderGroup } from "../library/border";
import { Bookmarks } from "../types/bookmark.type";

interface BorderGraphProps {
  commits: Bookmarks;
  width?: number;
  rowHeight: number;
  branchColors: Record<string, string>;
}

const BorderGraph: React.FC<BorderGraphProps> = ({
  commits,
  width = 10,
  rowHeight,
  branchColors,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const height = commits.length * rowHeight;

  useEffect(() => {
    const container = d3.select(svgRef.current);
    container.selectAll("*").remove(); // Clear previous content

    const svg = container
      .attr("class", "border-graph-container")
      .attr("width", width)
      .attr("height", height)
      .append("g");

    drawBorderGroup(
      svg,
      commits,
      rowHeight,
      branchColors,
      "BT",
    );
  }, [commits, width, height, rowHeight, branchColors]);

  return <svg ref={svgRef}></svg>;
};

export default BorderGraph;