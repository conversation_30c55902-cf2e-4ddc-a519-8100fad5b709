import VSCodeService from '@/services/vscode-api-service';
import { VscodeContextMenu, VscodeContextMenuItem } from '@vscode-elements/react-elements';
import React, { useEffect, useState } from 'react';

interface MenuItem {
  label: string;
  action: () => void;
}

interface ContextMenuProps {
  menuItems: MenuItem[];
  children: React.ReactNode;
  isOpen: boolean;
  setIsOpen: (state: boolean) => void;
  onHover?: any;
  onLeave?: any;
  style?: React.CSSProperties;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ menuItems, children, isOpen, setIsOpen, onHover, onLeave, style }) => {
  const vscode = VSCodeService.getInstance();

  const [themeColors, setThemeColors] = useState({
    background: '#252526',
    foreground: '#ffffff',
    border: '#3c3c3c',
    hover: '#007acc',
  });

  const updateThemeColors = () => {
    vscode.postMessage({ command: 'getThemeColors' });
  };

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      if (message.command === 'updateThemeColors') {
        setThemeColors(message.colors);
      }
    };

    window.addEventListener('message', handleMessage);

    updateThemeColors();

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  useEffect(() => {
    const disposable = vscode.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration('workbench.colorTheme')) {
        updateThemeColors();
      }
    });

    return () => disposable.dispose();
  }, [vscode]);

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsOpen(true);

  };

  useEffect(() => {
    const handleClickOutside = () => setIsOpen(false);
    if (isOpen) document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isOpen]);

  return (
    <div
      onContextMenu={handleContextMenu}
      onMouseEnter={onHover}
      onMouseLeave={onLeave}
    >
      {children}
      {isOpen && (
        <div
          className="fixed shadow-lg z-50 p-1 rounded"
          style={{
            ...style,
            backgroundColor: themeColors.background,
            color: themeColors.foreground,
            border: `1px solid ${themeColors.border}`,
            minWidth: '150px',
            transform: 'translateX(-100%)',
          }}
        >
          <ul className="py-1">
            {menuItems.map((item, index) => (
              <li
                key={index}
                className="px-4 py-2 cursor-pointer rounded"
                onClick={() => {
                  item.action();
                  setIsOpen(false);
                }}
                style={{
                  transition: 'background-color 0.2s',
                }}
                onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = themeColors.hover)}
                onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = themeColors.background)}
              >
                {item.label}
              </li>
            ))}

          </ul>
        </div>
      )}
    </div>
  );
};

export default ContextMenu;