import React, { useEffect, useRef, useState } from 'react';

import * as d3 from 'd3';

import { drawBranchLines, drawBranchNames } from '../library/branch';
import { drawCommit } from '../library/commit';
import { drawBackgroundGroup } from "../library/background"; 
import { setupContainer } from '../library/container';
import { Bookmark, Bookmarks } from '../types/bookmark.type';

interface SnapshotGraphProps {
  commits: Bookmarks;
  width?: number;
  height?: number;
  backgroundWidth?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  selectedCloneId?: any;
  rowHeight: number; // Add this prop to control row height
  currentSnapshotId?: string | null;
  branchConfig?: {
    spacing?: number;
    showNames?: boolean;
    showVerticalBar?: boolean;
    lineWidth?: number;
    pathType?: 'solid' | 'dashed' | 'double' | 'dotted';
    pathWidth?: number;
    branchSorting?: 'default' | 'lastFirst' | 'onlyLastFirst';
    hoverEffect?: boolean;
    directionV?: 'TB' | 'BT',
    directionH?: 'LR' | 'RL'
  };

  commitConfig?: {
    dotRadius?: number;
    dotHoveredRadius?: number;
    fontSize?: number;
    dotType?: 'circle' | 'square';
    showTooltip?: boolean;
    showName?: boolean;
    enableAnimations?: boolean;
    spacing?: number;
  };

  tooltipConfig?: {
    position?: 'left' | 'right' | 'top' | 'bottom';
    fontSize?: number;
  };

  colors?: readonly string[];
  onBranchColorsChange?: (branchColors: Record<string, string>) => void; // Add this prop
}

const SnapshotGraph: React.FC<SnapshotGraphProps> = ({
  commits,
  width = 100,
  height = 1000,
  backgroundWidth = 100,
  margin = { top: 50, right: 20, bottom: 20, left: 20 },
  selectedCloneId,
  rowHeight,
  currentSnapshotId,
  branchConfig = {
    spacing: 100,
    showNames: true,
    showVerticalBar: true,
    lineWidth: 1,
    pathType: 'solid',
    pathWidth: 1,
    branchSorting: 'lastFirst',
    hoverEffect: true,
    directionV: 'BT',
    directionH: 'LR',
  },

  commitConfig = {
    dotRadius: 4,
    dotHoveredRadius: 8,
    fontSize: 12,
    dotType: 'circle',
    showTooltip: true,
    showName: true,
    enableAnimations: true,
    spacing: 50,
  },

  tooltipConfig = {
    position: 'right',
    fontSize: 12,
  },

  colors = d3.schemeCategory10,
  onBranchColorsChange,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [branchColors, setBranchColors] = useState<Record<string, string>>({}); // Store branch colors

  useEffect(() => {
    const container = d3.select(svgRef.current);
    container.selectAll('*').remove(); // Clear previous content

    let branches = Array.from(
      new Set(commits.map((commit) => commit.dataset_id)),
    );

    // Calculate adjusted margin after branches are determined
    const adjustedMargin = {
      ...margin,
      left: -60 + margin.left + (branches.length - 1) * branchConfig.spacing!,
    };

    const svg = setupContainer(
      container as d3.Selection<SVGSVGElement, unknown, null, undefined>,
      width,
      height,
      adjustedMargin, // Use the dynamically adjusted margin
    );

    // Identify the last snapshot for each branch
    const lastSnapshots = new Set<string>(); // A set to hold IDs of the latest snapshots in each branch
    const sortedCommits = [...commits].sort(
      (a, b) => Number(a.created_date) - Number(b.created_date),
    ); // Sort commits by created_date in ascending order
    branches.forEach((branch) => {
      const lastCommit = sortedCommits
        .filter((commit) => commit.dataset_id === branch)
        .pop(); // Get the last commit in the sorted list for this branch
      if (lastCommit) {
        lastSnapshots.add(lastCommit.id); // Add the last commit's id to the set
      }
    });

    // Check if the selected clone has a last snapshot and store its ID
    const lastSelectedSnapshotId = sortedCommits
      .filter((commit) => commit.dataset_id === selectedCloneId)
      .pop()?.id;

    if (branchConfig.branchSorting === 'lastFirst') {
      const branchLastCommitMap: Record<string, Date> = {};
      commits.forEach((commit) => {
        branchLastCommitMap[commit.dataset_id] = new Date(
          Number(commit.created_date) * 1000,
        );
      });

      branches.sort(
        (a, b) =>
          (branchLastCommitMap[b]?.getTime() || 0) -
          (branchLastCommitMap[a]?.getTime() || 0),
      );
    } else if (branchConfig.branchSorting === 'onlyLastFirst') {
      const branchLastCommitMap: Record<string, Date> = {};
      commits.forEach((commit) => {
        branchLastCommitMap[commit.id] = new Date(Number(commit.created_date) * 1000);
      });

      const lastUsedBranch = branches.reduce(
        (latestBranch = '', currentBranch) => {
          return (branchLastCommitMap[currentBranch]?.getTime() || 0) >
            (branchLastCommitMap[latestBranch]?.getTime() || 0)
            ? currentBranch
            : latestBranch;
        },
        branches[0],
      );

      branches = [
        lastUsedBranch || '',
        ...branches.filter((branch) => branch !== lastUsedBranch),
      ];
    }

    const xScale = d3
      .scalePoint()
      .domain(branches)
      .range([
        0, // Fix the leftmost branch position
        (branches.length - 1) * branchConfig.spacing!,
      ].reverse()); // Reverse the range for LR direction

    const yScale = d3
      .scalePoint()
      .domain(commits.map((commit) => commit.id))
      .range(
        branchConfig.directionV === "TB"
          ? [0, (commits.length - 1) * commitConfig.spacing!] // Top-to-Bottom
          : [(commits.length - 1) * commitConfig.spacing!, 0] // Bottom-to-Top
      );
    
    

    const colorScale = d3.scaleOrdinal(colors);
    const newBranchColors: Record<string, string> = {};
    branches.forEach((branch) => {
      newBranchColors[branch] = colorScale(branch);
    });

    if (JSON.stringify(newBranchColors) !== JSON.stringify(branchColors)) {
      setBranchColors(newBranchColors);
      if (onBranchColorsChange) {
        onBranchColorsChange(newBranchColors);
      }
    }
    
    const commitDict: Record<string, Bookmark> = {};
    commits.forEach((commit) => {
      commitDict[commit.id] = commit;
    });
    
    drawBackgroundGroup(
      svg, 
      commits, 
      xScale, 
      rowHeight, 
      newBranchColors,
      branchConfig.directionV  || "TB",
      branchConfig.directionH || "LR",
    );
    
    const pathsGroup = svg.append('g').attr('class', 'paths-group');
    const dotsGroup = svg.append('g').attr('class', 'dots-group');

    drawBranchLines(
      pathsGroup,
      branches,
      xScale,
      height,
      margin,
      branchConfig.lineWidth!,
      branchConfig.showVerticalBar!,
      branchConfig.directionV  || "TB",
      branchConfig.directionH || "LR",
    );

    drawBranchNames(
      svg,
      branches,
      xScale,
      commitConfig.fontSize!,
      branchConfig.showNames!,
      branchConfig.directionV  || "TB",
      branchConfig.directionH || "LR",
    );

    commits.forEach((commit) => {
      const x = xScale(commit.dataset_id)!;
      const y = yScale(commit.id)!;

      const showRing = commit.id === currentSnapshotId;
      drawCommit(
        pathsGroup,
        dotsGroup,
        commit,
        x,
        y,
        commitConfig.dotType!,
        commitConfig.dotRadius!,
        commitConfig.dotHoveredRadius!,
        colorScale(commit.dataset_id!),
        commitConfig.enableAnimations!,
        commitConfig.showTooltip!,
        commitConfig.showName!,
        commitConfig.fontSize!,
        tooltipConfig,
        commitDict,
        branchConfig.pathType!,
        branchConfig.pathWidth!,
        xScale,
        yScale,
        showRing,
        branchConfig.directionV  || "TB",
        branchConfig.directionH || "LR",
      );
    });
  }, [
    commits,
    width,
    height,
    margin,
    branchConfig,
    commitConfig,
    tooltipConfig,
    colors,
    selectedCloneId,
    rowHeight,
    currentSnapshotId,
  ]);

  return <svg ref={svgRef}></svg>;
};

export default SnapshotGraph;
