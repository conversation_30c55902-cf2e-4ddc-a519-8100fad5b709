import * as d3 from "d3";
import { Bookmarks } from "../types/bookmark.type";

export function drawBackgroundGroup(
  svg: d3.Selection<SVGGElement, unknown, null, undefined>,
  commits: Bookmarks,
  xScale: d3.ScalePoint<string>,
  rowHeight: number,
  branchColors: Record<string, string>,
  directionV: "TB" | "BT", // Direction verticale
  directionH: "LR" | "RL" // Direction horizontale
) {
  const backgroundGroup = svg.append("g").attr("class", "background-group");

  // Ajuster les Y pour respecter TB / BT
  const totalRows = commits.length; // Nombre total de commits

  commits.forEach((commit, index) => {
    // Position X basé sur LR / RL
    const commitXPosition =
      directionH === "RL" ? xScale(commit.dataset_id)! : -xScale(commit.dataset_id)!;

    // Calculer la position Y basée sur TB / BT
    const rowIndex = directionV === "TB" ? index : totalRows - index - 1;
    const commitYPosition = rowIndex * rowHeight;
    const badgeXPosition =
      directionH === "LR" ? 400 : 400;
    const backgroundWidth =
      directionH === "RL"
        ? commitXPosition - badgeXPosition
        : badgeXPosition - commitXPosition;


    // Détecter les snapshots HEAD
    const isHeadSnapshot = commit.snapshot_type === "HEAD";
    const fillColor = isHeadSnapshot
      ? "#7f7f80"
      : branchColors[commit.dataset_id] ?? "#cccccc";

    // Rectangle principal pour chaque ligne
    backgroundGroup
      .append("rect")
      .attr("x", commitXPosition)
      .attr("y", commitYPosition - rowHeight / 2 + 2) // Centrer autour de Y
      .attr("width", backgroundWidth)
      .attr("height", rowHeight - 4)
      .attr("fill", fillColor)
      .attr("transparency", 0.1)
      .attr("opacity", 0.1); // Adjusted opacity for head snapshots

    // // Barre latérale
    // const sideColor = isHeadSnapshot
    //   ? "#7f7f80"
    //   : d3.color(branchColors[commit.dataset_id] ?? "#cccccc")?.toString() || "#ffffff";

    /* backgroundGroup
      .append("rect")
      .attr("x", directionH === "RL" ? commitXPosition : badgeXPosition)
      .attr("y", commitYPosition - rowHeight / 2 + 2) // Ajuster pour centrer
      .attr("width", 3)
      .attr("height", rowHeight) // Ajuster légèrement la hauteur
      .attr("fill", fillColor);
   */
  });
}
