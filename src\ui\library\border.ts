import * as d3 from "d3";
import { Bookmarks } from "../types/bookmark.type";

export function drawBorderGroup(
  svg: d3.Selection<SVGGElement, unknown, null, undefined>,
  commits: Bookmarks,
  rowHeight: number,
  branchColors: Record<string, string>,
  directionV: "TB" | "BT", // Direction verticale
) {
  const backgroundGroup = svg.append("g").attr("class", "background-group");

  // Ajuster les Y pour respecter TB / BT
  const totalRows = commits.length; // Nombre total de commits

  commits.forEach((commit, index) => {

    // Calculer la position Y basée sur TB / BT
    const rowIndex = directionV === "TB" ? index : totalRows - index - 1;
    const commitYPosition = rowIndex * rowHeight;

    // Détecter les snapshots HEAD
    const isHeadSnapshot = commit.snapshot_type === "HEAD";
    const fillColor = isHeadSnapshot
      ? "#7f7f80"
      : branchColors[commit.dataset_id] ?? "#cccccc";

    // Barre latérale
    const sideColor = isHeadSnapshot
      ? "#7f7f80"
      : d3.color(branchColors[commit.dataset_id] ?? "#cccccc")?.toString() || "#ffffff";

    backgroundGroup
      .append("rect")
      .attr("x", 0)
      .attr("y", commitYPosition - rowHeight / 2 + 2) // Ajuster pour centrer
      .attr("width", 3)
      .attr("height", rowHeight - 4) // Ajuster légèrement la hauteur
      .attr("fill", sideColor);
  });
}
