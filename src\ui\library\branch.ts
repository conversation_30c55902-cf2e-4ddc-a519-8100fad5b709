import * as d3 from "d3";

export function drawBranchLines(
  svg: d3.Selection<SVGGElement, unknown, null, undefined>,
  branches: string[],
  xScale: d3.ScalePoint<string>,
  height: number,
  margin: { top: number; right: number; bottom: number; left: number },
  lineWidth: number,
  showVerticalBar: boolean,
  direction: "TB" | "BT",
  directionH: "LR" | "RL" // Add directionH parameter
) {
  if (showVerticalBar) {
    // Reverse branch order if directionH is "rightToLeft"
    const sortedBranches =
      directionH === "RL" ? branches : [...branches].reverse();

    sortedBranches.forEach((branch, index) => {
      // Position branches based on their index in sorted order
      const x =
        directionH === "RL"
          ? xScale(branch)! + index * 50 // Incremental spacing
          : xScale(branch)! - index * 50; // Decremental spacing for RTL

      const yStart = direction === "TB" ? 0 : height - margin.top - margin.bottom;
      const yEnd = direction === "TB" ? height - margin.top - margin.bottom : 0;

      svg
        .append("line")
        .attr("x1", x)
        .attr("y1", yStart)
        .attr("x2", x)
        .attr("y2", yEnd)
        .attr("stroke", "#ccc")
        .attr("stroke-width", lineWidth)
        .attr("class", `branchLine branch-${branch}`);
    });
  }
}

export function drawBranchNames(
  svg: d3.Selection<SVGGElement, unknown, null, undefined>,
  branches: string[],
  xScale: d3.ScalePoint<string>,
  fontSize: number,
  showNames: boolean,
  direction: "TB" | "BT",
  directionH: "LR" | "RL" // Add directionH parameter
) {
  if (showNames) {
    // Reverse branch order if directionH is "rightToLeft"
    const sortedBranches =
      directionH === "RL" ? branches : [...branches].reverse();

    sortedBranches.forEach((branch, index) => {
      // Position branch names based on sorted order
      const x =
        directionH === "RL"
          ? xScale(branch)! + index * 50
          : xScale(branch)! - index * 50;
      const y = direction === "TB" ? -10 : 10;

      svg
        .append("text")
        .attr("x", x)
        .attr("y", y)
        .text(branch)
        .attr("fill", "black")
        .attr("font-size", fontSize)
        .attr("class", `branchName branch-${branch}`);
    });
  }
}

export function drawPath(
  svg: d3.Selection<SVGGElement, unknown, null, undefined>,
  startx: number,
  starty: number,
  endx: number,
  endy: number,
  color: string,
  pathType: "solid" | "dashed" | "double" | "dotted",
  pathWidth: number,
  multiBranch: boolean,
  targetBranch: string,
  delay: number,
  enableAnimations: boolean,
  gapToCircle = 0,
  directionV: "TB" | "BT",
  directionH: "LR" | "RL"
) {
  const totalLength = Math.abs(endy - starty);
  const fixedSegment2 = 20; // Fixed length for the second segment
  const segment1 = (totalLength - fixedSegment2) * (multiBranch ? 0 : 0); // First segment length
  const segment3 = totalLength - segment1 - fixedSegment2; // Remaining length

  // Adjust for direction (top-to-bottom or bottom-to-top)
  const adjustedStartY = directionV === "BT" ? starty - gapToCircle : starty + gapToCircle;
  const adjustedEndY = directionV === "BT" ? endy + gapToCircle : endy - gapToCircle;

  const adjustedStartX = directionH === "LR" ? startx : -startx;
  const adjustedEndX = directionH === "LR" ? endx : -endx;

  const controlPoint1Y = adjustedStartY + (directionV === "BT" ? -segment1 : segment1);
  const controlPoint2Y = adjustedEndY + (directionV === "BT" ? segment3 : -segment3);

  const path = svg
    .append("path")
    .attr(
      "d",
      `M ${adjustedStartX} ${adjustedStartY}
       L ${adjustedStartX} ${controlPoint1Y} 
       C ${adjustedStartX} ${controlPoint1Y + fixedSegment2 * (directionV === "TB" ? 1 : -1)}, 
         ${adjustedEndX} ${controlPoint1Y + fixedSegment2 * (directionV === "TB" ? 0.1 : -0.1)}, 
         ${adjustedEndX} ${controlPoint2Y}
       L ${adjustedEndX} ${adjustedEndY}`
    )
    .attr("stroke", color)
    .attr("stroke-width", pathWidth)
    .attr("fill", "none")
    .attr("class", `branchPath branch-${targetBranch}`)
    .attr("stroke-dasharray", function() {
      return enableAnimations ? this.getTotalLength() : "none";
    })
    .attr("stroke-dashoffset", function () {
      return this.getTotalLength();
    });

  if (enableAnimations) {
    path
      .transition()
      .duration(enableAnimations ? 1000 : 0)
      .delay(delay)
      .ease(d3.easeCubic)
      .attr("stroke-dashoffset", 0)
      .on("end", () => {
        d3.selectAll(`.commitDot.branch-${targetBranch}`).attr("opacity", 1);
      });
  } else {
    path.attr("stroke-dashoffset", 0);
  }

  // Handle additional path types
  if (pathType === "dashed") {
    path.attr("stroke-dasharray", "4,2");
  } else if (pathType === "dotted") {
    path.attr("stroke-dasharray", "1,4");
  } else if (pathType === "double") {
    path
      .attr("stroke", color)
      .transition()
      .duration(1000)
      .delay(delay)
      .ease(d3.easeCubic)
      .attr("stroke-width", pathWidth * 2)
      .attr("stroke", color)
      .attr("opacity", 0.5);
  }
}