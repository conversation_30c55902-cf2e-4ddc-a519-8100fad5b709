import * as d3 from "d3";
import { drawPath } from "./branch";
import { showCard, hideCard } from "./tooltip";
import { Snapshot } from "../types/instant-db/snapshot.type";
import { Bookmark } from "../types/bookmark.type";

export function drawCommit(
  pathsGroup: d3.Selection<SVGGElement, unknown, null, undefined>,
  dotsGroup: d3.Selection<SVGGElement, unknown, null, undefined>,
  commit: Bookmark,
  x: number,
  y: number,
  dotType: "circle" | "square",
  dotRadius: number,
  dotHoveredRadius: number,
  color: string,
  enableAnimations: boolean,
  showTooltip: boolean,
  showCommitName: boolean,
  fontSize: number,
  tooltipConfig: { position?: string; fontSize?: number },
  commitDict: Record<string, Bookmark>,
  pathType: "solid" | "dashed" | "double" | "dotted",
  pathWidth: number,
  xScale: d3.ScalePoint<string>,
  yScale: d3.ScalePoint<string>,
  showRing: boolean,
  directionV: "TB" | "BT", // Abbreviated vertical direction
  directionH: "LR" | "RL" // Abbreviated horizontal direction
) {
  const isHeadSnapshot = commit.snapshot_type === "HEAD";

  // Adjust horizontal position based on directionH
  const adjustedX = directionH === "RL" ? x : -x;
  
  const headPathColor = "#7f7f80"; // Custom color for head path and dot
  const headDotColor = isHeadSnapshot ? headPathColor : color; // Use head color if it's a head snapshot
  
  let currentDotType = showRing ? "square" : dotType;

  // Change shape or style for commits with status "AUTO"
  if (commit.snapshot_type === "AUTO") {
    currentDotType = "square"; 
    dotRadius = dotRadius*1.5;
  }

  const backgroundColor = window.getComputedStyle(document.body).backgroundColor;  
  if (showRing) {
    const dot = dotsGroup
      .append("circle")
      .attr("cx", adjustedX)
      .attr("cy", y)
      .attr("r", dotRadius + 1.5) // Adjust this value to scale the ring size
      .attr("fill", backgroundColor)
      .attr("stroke", headDotColor) // Stroke color for the ring effect
      .attr("stroke-width", 2) // Stroke width for ring thickness
      .attr("class", `commitDot ring branch-${commit.dataset_id}`)
      .style("pointer-events", "visible")
      .attr("opacity", enableAnimations ? 0 : 1);

    if (enableAnimations) {
      dot.transition().duration(500).attr("opacity", 1);
    }

    if (showTooltip) {
      dot.on("mouseover", function () {
        d3.selectAll(".branchLine")
          .filter( () => {
            return !d3.select(this).classed(`branch-${commit.dataset_id}`);
          })
          .transition()
          .duration(200)
          .attr("opacity", 0.3);

        d3.selectAll(".branchPath")
          .filter( () => {
            return !d3.select(this).classed(`branch-${commit.dataset_id}`);
          })
          .transition()
          .duration(200)
          .attr("opacity", 0.3);

        d3.selectAll(".commitDot")
          .filter( () => {
            return !d3.select(this).classed(`branch-${commit.dataset_id}`);
          })
          .transition()
          .duration(200)
          .attr("opacity", 0.3);

        d3.selectAll(`.branch-${commit.dataset_id}`)
          .transition()
          .duration(200)
          .attr("stroke-width", pathWidth * 2)
          .attr("opacity", 1);

        d3.select(this)
          .transition()
          .duration(200)
          .attr("r", dotHoveredRadius); // Adjust on hover

        showCard(commit.id, x, y, tooltipConfig, commit.snapshot_type);
      }).on("mouseout", function () {
        d3.selectAll(".branchLine").transition().duration(200).attr("opacity", 1);
        d3.selectAll(".branchPath").transition().duration(200).attr("opacity", 1);
        d3.selectAll(`.branch-${commit.dataset_id}`).transition().duration(200).attr("opacity", 1).attr("stroke-width", pathWidth);
        d3.selectAll(".commitDot").transition().duration(200).attr("opacity", 1);
        d3.select(this).transition().duration(200).attr("r", dotRadius); // Return to original size
          hideCard();
        });
    }
  } else {
    const dot = dotsGroup
      .append(currentDotType === "circle" ? "circle" : "rect")
      .attr(
        currentDotType === "circle" ? "cx" : "x",
        adjustedX - (currentDotType === "square" ? dotRadius / 2 : 0)
      )
      .attr(
        currentDotType === "circle" ? "cy" : "y",
        y - (currentDotType === "square" ? dotRadius / 2 : 0)
      )
      .attr(currentDotType === "circle" ? "r" : "width", dotRadius)
      .attr(currentDotType === "circle" ? "r" : "height", dotRadius)
      .attr("fill", headDotColor)
      .attr("class", `commitDot branch-${commit.dataset_id}`)
      .style("pointer-events", "visible")
      .attr("opacity", enableAnimations ? 0 : 1);

    if (enableAnimations) {
      dot.transition().duration(500).attr("opacity", 1);
    }

    if (showTooltip) {
      dot
        .on("mouseover", function () {
          d3.select(this)
          .filter(function () {
            return !d3.select(this).classed(`branch-${commit.dataset_id}`);
          })
            .transition()
            .duration(200)
          .attr("opacity", 0.3);

        d3.selectAll(".branchPath")
          .filter(function () {
            return !d3.select(this).classed(`branch-${commit.dataset_id}`);
          })
          .transition()
          .duration(200)
          .attr("opacity", 0.3);

        d3.selectAll(".commitDot")
          .filter(function () {
            return !d3.select(this).classed(`branch-${commit.dataset_id}`);
          })
          .transition()
          .duration(200)
          .attr("opacity", 0.3);

        d3.selectAll(`.branch-${commit.dataset_id}`)
          .transition()
          .duration(200)
          .attr("stroke-width", pathWidth * 2)
          .attr("opacity", 1);

        d3.selectAll(`.ring.branch-${commit.dataset_id}`)
          .transition()
          .duration(200)
          .attr("stroke-width", pathWidth );

          d3.select(this)
            .transition()
            .duration(200)
          .attr(currentDotType === "circle" ? "r" : "width", dotHoveredRadius)
          .attr(currentDotType === "circle" ? "r" : "height", dotHoveredRadius);

        showCard(commit.id, x, y, tooltipConfig, commit.snapshot_type);
      }).on("mouseout", function () {
        d3.selectAll(".branchLine").transition().duration(200).attr("opacity", 1);
        d3.selectAll(".branchPath").transition().duration(200).attr("opacity", 1);
        d3.selectAll(`.branch-${commit.dataset_id}`).transition().duration(200).attr("opacity", 1).attr("stroke-width", pathWidth);
        d3.selectAll(".commitDot").transition().duration(200).attr("opacity", 1);
        d3.select(this).transition().duration(200).attr(currentDotType === "circle" ? "r" : "width", dotRadius).attr(currentDotType === "circle" ? "r" : "height", dotRadius);
          hideCard();
        });
    }
  }

  if (showCommitName) {
    dotsGroup
      .append("text")
      .attr("x", adjustedX + 10)
      .attr("y", y)
      /* .text(commit.name.includes("main") ? commit.cloneName : commit.name) */
      .attr("fill", "black")
      .attr("font-size", fontSize)
      .attr("class", `commitName branch-${commit.dataset_id}`);
  }

  if (commit.parent_id) {
    const parentCommit = commitDict[commit.parent_id];
    if (parentCommit) {
      const parentX = xScale(parentCommit.dataset_id)!;
      const parentY = yScale(parentCommit.id)!;

      const adjustedParentX =
        directionH === "RL" ? parentX : -parentX; // Adjust for horizontal direction

      const multiBranch = Object.values(commitDict).filter(
        (c) => c.parent_id === parentCommit.id
      ).length > 1;

      const delay =
        (Object.keys(commitDict).indexOf(commit.id) + 1) * 200; // Delay each path by 200ms multiplied by its order

      const selectedPathType = isHeadSnapshot ? "dashed" : pathType;
      const headPathColor = isHeadSnapshot ? "#7f7f80" : color;
      const gapToCircle = 0;
      drawPath(
        pathsGroup,
        adjustedParentX,
        parentY,
        adjustedX,
        y,
        headPathColor,
        selectedPathType,
        pathWidth,
        multiBranch,
        commit.dataset_id,
        delay,
        enableAnimations,
        0,
        directionV,
        directionH // Pass horizontal direction
      );
    }
  }
}
