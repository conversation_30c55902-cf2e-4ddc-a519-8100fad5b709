import * as d3 from "d3";

export function setupContainer(
  container: d3.Selection<SVGSVGElement, unknown, null, undefined>,
  width: number,
  height: number,
  margin: { top: number; right: number; bottom: number; left: number }
) {
  const containerWidth = Math.max(width);

  return container
    .attr("class", "snapshot-graph-container")
    .attr("width", containerWidth)
    .attr("height", "100%")
    .style("min-width", containerWidth + 160)
    .append("g")
    .attr("transform", `translate(${margin.left},${margin.top})`);
}