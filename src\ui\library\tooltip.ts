import * as d3 from "d3";

export function showCard(
  commitId: string,
  x: number,
  y: number,
  config: { position?: string; fontSize?: number },
  snapshot_type: string
) {
  const hoverCard = d3.select("#hoverCardContainer");
  let left = x + 20,
    top = y - 25;
  if (config.position === "left") {
    left = x - 120;
  } else if (config.position === "top") {
    top = y - 50;
  } else if (config.position === "bottom") {
    top = y + 10;
  }
  hoverCard
    .style("left", `${left}px`)
    .style("top", `${top}px`)
    .style("display", "block")
    .style("font-size", `${config.fontSize}px`);
  const message = snapshot_type === "AUTO" ? "Commit: Auto Snapshot" : `Commit: ${commitId}`;
  hoverCard.select("#commit-time-message").text(message);}

export function hideCard() {
  d3.select("#hoverCardContainer").style("display", "none");
}