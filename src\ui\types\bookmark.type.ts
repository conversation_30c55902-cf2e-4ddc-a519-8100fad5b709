export type Bookmark = {
    id: string,
    name: string,
    status: string,
    snapshot_type:string,
    snapshot_comment: string,
    snapshot_schema: any,
    customer_id: string,
    dataset_id: string,
    deployment_id: string,
    is_ephemeral: boolean,
    is_golden: string,
    parent_id: string | undefined,
    created_by: string,
    created_date: number,
    last_modified_by: string,
    last_modified_date: number
};

export type CreateBookmarkRequest = {
    repositoryId: string,
    branchId: string,
    snapshot_comment: string,
};

export type Bookmarks = Bookmark[];