export type Branch = {
    id: string;
    name: string;
    branch_name:string;
    customer_id: string;
    snapshot_id: string;
    deployment_id: string;
    environment_type: string;
    database_provider: string;
    database_version: string;
    database_username: string;
    database_password: string;
    is_ephemeral: boolean;
    is_masked: boolean;
    is_purged: boolean;
    created_by: string;
    created_date: string;
    last_modified_by: string;
    last_modified_date: string;
    performance_profile_id: string;
};

export type CreateBranchRequest = {
    repositoryId: string,
    branchId: string,
    bookmarkId: string,
    discard_changes : string,
    checkout : boolean,
    ephemeral: boolean,
    branch_name?: string,
};

export type CheckoutBookmarkRequest = {
    repositoryId: string,
    branchId: string,
    bookmarkId: string,
    discard_changes : string,
    checkout : boolean,
    ephemeral: boolean,
};

export type CheckoutBranchRequest = {
    repositoryId: string,
    branchId: string,
};

export type Branches = Branch[];
