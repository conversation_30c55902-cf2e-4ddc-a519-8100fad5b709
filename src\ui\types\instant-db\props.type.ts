import { Snapshots } from "./snapshot.type";
import { Deployments } from "./deployment.type";

export type AppProps = {
  statusMessage: boolean;
  deploymentId: string;
  cloneId: string;
  selectedCloneId: string;
  loading: boolean;
  accessToken: string;
  setLoading: (loading: boolean) => void;
  setSelectedCloneId: (id: string) => void;
}

export type LogProps = {
  loading: boolean;
  logs: any;
  cloneName: string;
}

export type DiffProps = {
  deploymentId: string;
  selectedCloneId: string;
  accessToken: string;
  snapshots: Snapshots;
  filteredSnapshots: Snapshots;
  clones: Deployments;
}

export type DatabaseAppProps = {
  deploymentId: string;
  cloneId: string;
  selectedCloneId: string;
  clones: Deployments;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  setSelectedCloneId: (id: string) => void;
  setRefresh: (refresh: boolean) => void;
}

export type AppPropsWithSnapshot = {
  repositoryId: string;
}

export type MonitoringProps = {
  deploymentId: string;
  cloneId: string;
  statusMessage: boolean;
  error: string | null;
  chartData: {
    time: string;
    memory: number | null;
    cpu: number | null;
  }[];
  isMonitoring: boolean;
  retryInterval: NodeJS.Timeout | null;
  memoryUsage: number | null;
  cpuUsage: number | null;
  maxMemoryUsage: number;
  maxCpuUsage: number;
  setChartData: (data: any) => void;
  setError: (value: string | null) => void;
  setIsMonitoring: (value: boolean) => void;
  setRetryInterval: (value: NodeJS.Timeout | null) => void;
  setMemoryUsage: (value: number | null) => void;
  setCpuUsage: (value: number | null) => void;
  setMaxMemoryUsage: (value: number) => void;
  setMaxCpuUsage: (value: number) => void;
  max_cpu: number;
  max_memory: number;
};

export type ComplianceProps = {
  deploymentId: string;
  selectedCloneId: string;
  statusMessage: boolean;
  tables: any[];
  complianceResults: any[];
  loading: boolean;
  searchQueries: { [key: string]: string };
  tablesLoading: boolean;
  analyzingAll: boolean;
  globalSearchQuery: string;
  progress: number;
  showSuccess: boolean;
  transformers: { [tableName: string]: { [columnName: string]: string } };
  maskingLoading: boolean;
  setTables: (value: any[]) => void;
  setComplianceResults: (value: any) => void;
  setLoading: (value: boolean) => void;
  setSearchQueries: (value: any) => void;
  setTablesLoading: (value: boolean) => void;
  setAnalyzingAll: (value: boolean) => void;
  setGlobalSearchQuery: (value: string) => void;
  setProgress: (value: number) => void;
  setShowSuccess: (value: boolean) => void;
  setTransformers: (value: any) => void;
  setMaskingLoading: (value: boolean) => void;
}
