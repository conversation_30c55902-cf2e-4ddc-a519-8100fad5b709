import { Bookmark, Bookmarks } from "../bookmark.type";

export type SnapshotGraphProps = {
    snapshots: Bookmarks;
    width?: number;
    height?: number;
    margin?: { top: number; right: number; bottom: number; left: number };

    branchConfig?: {
        spacing?: number;
        showNames?: boolean;
        showVerticalBar?: boolean;
        lineWidth?: number;
        pathType?: "solid" | "dashed" | "double" | "dotted";
        pathWidth?: number;
        branchSorting?: "default" | "lastFirst" | "onlyLastFirst";
        hoverEffect?: boolean;
    };

    snapshotConfig?: {
        dotRadius?: number;
        dotHoveredRadius?: number;
        fontSize?: number;
        dotType?: "circle" | "square";
        showTooltip?: boolean;
        showName?: boolean;
        enableAnimations?: boolean;
        spacing?: number;
    };

    tooltipConfig?: {
        position?: "left" | "right" | "top" | "bottom";
        fontSize?: number;
    };

    colors?: readonly string[];
    onsnapshotHover?: (snapshot: Bookmark | null) => any;
}