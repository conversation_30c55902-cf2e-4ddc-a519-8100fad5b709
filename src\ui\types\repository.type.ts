export type Repository = {
    id: string;
    name: string;
    status: string;
    repository_name: string;
    clone_id: string;
    snapshot_id: string;
    fqdn: string;
    database_provider: string;
    database_version: string;
    region: string;
    instance_type: string;
    created_by:string;
    created_date: string;
    customer_id: string;
    database_password: string;
    deployment_parent: string | null;
    deployment_type: string;
    datacenter: string;
    snapshot_parent: string;
};

export type CreateShadowRequest = {
    repositoryId: string,
    branchId: string,
    bookmarkId: string,
    repository_name:string,
    branch_name : string,
    performance_profile_id: string;
};

export type Repositories = Repository[];