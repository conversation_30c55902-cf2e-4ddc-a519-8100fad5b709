import { Branch, Branches } from '@/ui/types/branch.type';
import { findManualParentId } from './parentFinder';
import { Bookmark, Bookmarks } from '../types/bookmark.type';

// Function to get the representer snapshot for each clone in a list of clones
export function getRepresentingSnapshotsForClones(
  clones: Branches,
  snapshots: Bookmarks,
) {
  return clones.map((clone) => {
    const representingSnapshot = getRepresentingSnapshot(clone, snapshots);
    return {
      clone,
      representingSnapshot,
    };
  });
}

function getRepresentingSnapshot(
  clone: Branch, // The current clone for which we are finding the representing snapshot
  snapshots: Bookmarks, // The list of all snapshots
): Bookmark | undefined {

  // Step 1: Filter snapshots that belong to the given clone by matching the clone_id
  const cloneSnapshots = snapshots.filter(
    (snapshot) => snapshot.dataset_id === clone.id,
  );

  // Step 2: If no snapshots are found for the clone, return undefined
  if (cloneSnapshots.length === 0) return undefined;

  // Step 3: Sort the clone's snapshots by the `created_date` in ascending order
  const sortedSnapshots = cloneSnapshots.sort(
    (a, b) => new Date(a.created_date).getTime() - new Date(b.created_date).getTime(),
  );

  // Step 4: Check if there is exactly 1 snapshot and if it's a 'MANUAL' snapshot
  if (
    cloneSnapshots.length === 1 &&
    cloneSnapshots[0]?.snapshot_type === 'MANUAL'
  ) {
    return cloneSnapshots[0]; // If it's a manual snapshot, return it as the representer
  }

  // Step 5: Check if there is exactly 1 snapshot and if it's an 'INIT' snapshot
  else if (
    cloneSnapshots.length === 1 &&
    cloneSnapshots[0]?.snapshot_type === 'INIT'
  ) {
    const initSnapshot = sortedSnapshots[0];
    // Find the manual parent snapshot by traversing through the parent chain
    let representerSnapshotId = findManualParentId(initSnapshot?.id, snapshots);
    let represnterSnapshot = snapshots.find(
      (snapshot) => snapshot.id === representerSnapshotId,
    );
    return represnterSnapshot; // Return the found manual parent snapshot
  }

  else if (
    cloneSnapshots.length === 2 &&
    sortedSnapshots.every((snapshot) => snapshot.snapshot_type === 'MANUAL')
  ) {
    // Return the latest MANUAL snapshot
    return sortedSnapshots[1];
  }

  // Step 6: Check if there are exactly 2 snapshots and both are either 'AUTO' or 'INIT'
  else if (
    cloneSnapshots.length === 2 &&
    (sortedSnapshots[0]?.snapshot_type === 'AUTO' ||
      sortedSnapshots[0]?.snapshot_type === 'INIT') &&
    (sortedSnapshots[1]?.snapshot_type === 'AUTO' ||
      sortedSnapshots[1]?.snapshot_type === 'INIT')
  ) {
    // If there are 2 snapshots (AUTO and INIT), neither is displayed, so we search for the first manual parent.
    const firstSnapshot = sortedSnapshots[0];
    let representerSnapshotId = findManualParentId(firstSnapshot?.id, snapshots);
    let represnterSnapshot = snapshots.find(
      (snapshot) => snapshot.id === representerSnapshotId,
    );
    return represnterSnapshot; // Return the found manual parent snapshot
  }

  else if (
    cloneSnapshots.length === 2 &&
    (sortedSnapshots[0]?.snapshot_type === 'MANUAL' ||
      sortedSnapshots[0]?.snapshot_type === 'AUTO') &&
    (sortedSnapshots[1]?.snapshot_type === 'MANUAL' ||
      sortedSnapshots[1]?.snapshot_type === 'AUTO')
  ) {
    // If there are 2 snapshots (MANUAL and INIT), we chose the manual.
    const representativeSnapshot = sortedSnapshots.find(snapshot => snapshot.snapshot_type === 'MANUAL') || sortedSnapshots[0];

    return representativeSnapshot; // Return the found manual parent snapshot
  }

  // Step 7: If there are 3 or more snapshots, search backward from the last one to find the first 'MANUAL' snapshot
  else if (cloneSnapshots.length >= 3) {
    for (let i = sortedSnapshots.length - 1; i >= 0; i--) {
      const snapshot = sortedSnapshots[i];
      if (snapshot?.snapshot_type === 'MANUAL') {
        return snapshot; // Return the first 'MANUAL' snapshot found
      }
    }
  }

  // Step 8: Default case – if no special condition is met, return the last snapshot in the sorted list
  return sortedSnapshots[sortedSnapshots.length - 1];
}