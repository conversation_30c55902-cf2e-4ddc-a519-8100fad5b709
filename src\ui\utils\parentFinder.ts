import { Bookmarks } from "../types/bookmark.type";

export const findManualParentId = (
  snapshotId: string | undefined,
  snapshots: Bookmarks,
): string | undefined => {
  let currentSnapshotId = snapshotId;

  // Continue searching until a manual snapshot is found
  while (currentSnapshotId) {
    const currentSnapshot = snapshots.find(
      (snap) => snap.id === currentSnapshotId,
    );
    // If we find a manual snapshot, stop and return its ID
    if (currentSnapshot?.snapshot_type === 'MANUAL') {
      return currentSnapshot.id;
    }

    // If current snapshot is hidden (snapshot_type = INIT or AUTO), move to its parent if parent_id exists
    if (
      currentSnapshot &&
      (currentSnapshot.snapshot_type === 'INIT' ||
        currentSnapshot.snapshot_type === 'AUTO') &&
      currentSnapshot.parent_id // Ensure parent_id exists
    ) {
      currentSnapshotId = currentSnapshot.parent_id; // Move up the chain
    } else {
      break; // Exit the loop if no parent_id is found
    }
  }

  return undefined; // If no manual snapshot was found in the chain
};
