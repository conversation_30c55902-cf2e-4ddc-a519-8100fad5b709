import { findManualParentId } from "@/ui/utils/parentFinder";
import { Bookmarks } from "../types/bookmark.type";

export const filterSnapshots = (snapshots: Bookmarks): Bookmarks => {
    const filteredSnapshots: Bookmarks = [];
    const parentIdMap: Record<string, string | undefined> = {}; // Map to store the parent_id of hidden snapshots

    snapshots.forEach((snapshot) => {
        if (
            snapshot.snapshot_type === 'INIT' ||
            snapshot.snapshot_type === 'AUTO'
        ) {
            // For hidden snapshots, store their parent_id in the map
            parentIdMap[snapshot.id] = snapshot.parent_id;
        }
    });


    snapshots.forEach((snapshot) => {
        if (
            snapshot.snapshot_type === 'INIT' ||
            snapshot.snapshot_type === 'AUTO'
        ) {
            // For hidden snapshots, store their parent_id in the map
            parentIdMap[snapshot.id] = snapshot.parent_id;
        } else {
            // For visible snapshots, check if their parent_id points to a hidden snapshot
            if (snapshot.parent_id && parentIdMap[snapshot.parent_id]) {
                // Search for the parent_id of a manual snapshot, starting from the current hidden parent
                const manualParentId = findManualParentId(snapshot.parent_id, snapshots);
                // If we find a manual snapshot, update the parent_id to that one
                if (manualParentId) {
                    snapshot.parent_id = manualParentId;
                }
            }

            // Add the processed snapshot to the list
            filteredSnapshots.push(snapshot);
        }
    });

    const sortedSnapshots = [...filteredSnapshots].sort(
        (a, b) => new Date(a.created_date).getTime() - new Date(b.created_date).getTime()
    ); // Sort commits by created_date in ascending order for displaying the graph and snaps_list.
    return sortedSnapshots;
};
