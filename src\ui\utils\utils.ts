function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}

const getCloneSerial = (cloneName: string): string => {
    const parts = cloneName.split('-');
    return parts.slice(2, 3).join('-');
};

const getShortCloneName = (cloneName: string): string => {
    const parts = cloneName.split('-');
    return parts.slice(0, 2).join('-'); // Join the first two parts
};

const getSnapshotHash = (snapshotId: string): string => {
    const parts = snapshotId.split('-');
    return parts.slice(0, 1).join('-'); // Join the first two parts
};

export const Utils = {
    getNonce,
    getCloneSerial,
    getShortCloneName,
    getSnapshotHash
};


