import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import { filterSnapshots } from "@/ui/utils/snapshotsFilter";
import * as d3 from "d3";
import SnapshotGraph from "@/ui/components/SnapshotGraph";
import { BookmarkIcon, Loader2 } from 'lucide-react';

import {
    VscodeButton,
    VscodeTextfield,
    VscodeSingleSelect,
    VscodeOption,
    VscodeSplitLayout,
    VscodeIcon,
    VscodeTabHeader,
    VscodeTableHeader,
    VscodeTableRow,
    VscodeTableHeaderCell,
    VscodeTable,
    VscodeTableBody,
} from "@vscode-elements/react-elements";

import { Deployment, Deployments } from "@/ui/types/instant-db/deployment.type";
import { Bookmark, Bookmarks, CreateBookmarkRequest } from "@/ui/types/bookmark.type";
import BorderGraph from "@/ui/components/BorderGraph";
import { Branch, Branches } from "@/ui/types/branch.type";
import { getRepresentingSnapshotsForClones } from "@/ui/utils/cloneRepresenter";
import { Compute } from "@/ui/types/compute.type";
import Badge from "../../../components/Badge";
import ContextMenu from "../../../components/ContextMenu";
import SnapshotTable from "./SnapshotTable";
import VSCodeApiService from "@/services/vscode-api-service";
import ComputeSection from "./repository-compute-section";
import Loader from "./Loader";
import { Repository } from "@/ui/types/repository.type";
import PopupWindow from "./popupWindow";


const defaultBranchConfig = {
    spacing: 12,
    showNames: false,
    showVerticalBar: false,
    lineWidth: 1,
    pathType: 'solid' as const,
    pathWidth: 2,
    branchSorting: 'lastFirst' as const,
    hoverEffect: true,
    directionV: 'BT',
    directionH: 'LR'
};

const defaultCommitConfig = {
    spacing: 30,
    dotRadius: 3,
    dotHoveredRadius: 7,
    fontSize: 14,
    dotType: 'circle' as const,
    showTooltip: true,
    showName: false,
    enableAnimations: false,
};

const tooltipConfig = {
    position: 'right' as const,
    fontSize: 12,
};

const calculateDotRadius = (pathWidth: number) => {
    const minPathWidth = 1;
    const maxPathWidth = 6;
    const minDotRadius = 3;
    const maxDotRadius = 7;

    const clampedPathWidth = Math.min(
        Math.max(pathWidth, minPathWidth),
        maxPathWidth,
    );

    const dotRadius =
        ((clampedPathWidth - minPathWidth) / (maxPathWidth - minPathWidth)) *
        (maxDotRadius - minDotRadius) +
        minDotRadius;

    return dotRadius;
};

const MainPanel: React.FC = () => {

    const vscode = VSCodeApiService.getInstance();

    const [imageSrc, setImageSrc] = useState("");

    const [deployments, setDeployments] = useState<Deployments>([]);
    const [selectedDeploymentId, setSelectedDeploymentId] = useState<string | null>(null);
    const [deploymentData, setDeploymentData] = useState<Deployment | null>(null);

    const [snapshots, setSnapshots] = useState<Bookmarks>([]);
    const [filteredSnapshots, setFilteredSnapshots] = useState<Bookmarks>([]);

    const [branches, setBranches] = useState<Branches>([]);
    const [compute, setCompute] = useState<Compute | null>(null);

    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    const [branchCount, setBranchCount] = useState<number | null>(null);

    const [graphWidth, setGraphWidth] = useState<number>(200);
    const [hoveredCommit, setHoveredCommit] = useState<Bookmark | null>(null);

    const [bookmarkMessage, setBookmarkMessage] = useState<string>('');
    const [isCreatingBookmark, setIsCreatingBookmark] = useState<boolean>(false);

    const [isEphemeralBranchDialogVisible, setIsEphemeralBranchDialogVisible] = useState(false);

    const [branchSorting, setBranchSorting] = useState<
        'default' | 'lastFirst' | 'onlyLastFirst'
    >(defaultBranchConfig.branchSorting);
    const [enableAnimations, setEnableAnimations] = useState<boolean>(
        defaultCommitConfig.enableAnimations,
    );
    const [showVerticalBar, setShowVerticalBar] = useState<boolean>(
        defaultBranchConfig.showVerticalBar,
    );
    const [pathWidth, setPathWidth] = useState<number>(
        defaultBranchConfig.pathWidth,
    );
    const [spacing, setSpacing] = useState<number>(defaultBranchConfig.spacing);
    const [dotRadius, setDotRadius] = useState<number>(
        calculateDotRadius(defaultBranchConfig.pathWidth),
    );
    const [currentSnapshotId, setCurrentSnapshotId] = useState<string | null>(
        null,
    );
    const [branchColors, setBranchColors] = useState<Record<string, string>>({});
    const [loadingButton, setLoadingButton] = useState<string | null>(null);

    const getDynamicSnapshots = useCallback((currentSnapshots: Bookmarks) => {
        const selectedBranchSnapshots = currentSnapshots.filter(snapshot => snapshot.dataset_id === compute?.attached_branch);
        const lastSnapshot = selectedBranchSnapshots[selectedBranchSnapshots.length - 1];

        const headSnapshot = lastSnapshot
            ? {
                ...lastSnapshot,
                id: `*`,
                snapshot_type: "HEAD" as "HEAD",
                parent_id: lastSnapshot.id,
                created_date: Date.now(),
                snapshot_comment: "... wip ...",
                created_by: "*",
            }
            : null;

        return headSnapshot ? [...currentSnapshots, headSnapshot] : currentSnapshots;
    }, [snapshots, compute?.attached_branch]);

    const [rowHeight, setRowHeight] = useState<number>(30);
    const [treeWidth, setTreeWidth] = useState(() => {
        const initialWidth = 10;
        return Math.min(initialWidth, 40);
    });
    const MIN_WIDTH = 10;
    const MAX_WIDTH = 40;

    const fetchAllDeployments = useCallback(() => {
        try {
            setLoading(true);
            setError(null);
            vscode.postMessage({ action: "GET_ALL_REPOSITORIES", data: {} });
        } catch (err) {
            setError("Failed to fetch deployments. Please try again.");
            console.error(err);
        }
    }, [vscode]);

    const fetchDeployment = useCallback((deploymentId: string) => {
        try {
            setLoading(true);
            setError(null);
            vscode.postMessage({ action: "GET_REPOSITORY", data: { deploymentId } });
        } catch (err) {
            setError("Failed to fetch deployments. Please try again.");
            console.error(err);
        }
    }, [vscode]);

    const fetchCompute = useCallback((deploymentId: string, cloneId: string) => {
        try {
            setLoading(true);
            setError(null);
            vscode.postMessage({ action: "GET_COMPUTE", data: { deploymentId, cloneId } });
        } catch (err) {
            setError("Failed to fetch compute. Please try again.");
            console.error(err);
        }
    }, [vscode]);

    const fetchBranch = useCallback((deploymentId: string, cloneId: string) => {
        try {
            setLoading(true);
            setError(null);
            vscode.postMessage({ action: "GET_BRANCH", data: { deploymentId, cloneId } });
        } catch (err) {
            setError("Failed to fetch branch. Please try again.");
            console.error(err);
        }
    }, [vscode]);

    const fetchAllSnapshots = useCallback((deploymentId: string) => {
        try {
            setLoading(true);
            setError(null);
            vscode.postMessage({ action: "GET_ALL_SNAPSHOTS", data: { deploymentId } });
        } catch (err) {
            setError("Failed to fetch snapshots. Please try again.");
            console.error(err);
        }
    }, [vscode]);

    const fetchAllBranches = useCallback((deploymentId: string) => {
        try {
            setLoading(true);
            setError(null);
            vscode.postMessage({ action: "GET_ALL_BRANCHES", data: { deploymentId } });
        } catch (err) {
            setError("Failed to fetch branches. Please try again.");
            console.error(err);
        }
    }, [vscode]);


    const createBookmark = useCallback((deploymentId: string, computeInstance: Compute | null) => {
        try {
            setIsCreatingBookmark(true);
            setError(null);

            if (!computeInstance) {
                throw new Error("Compute data is not available");
            }

            const bookmarkReq: CreateBookmarkRequest = {
                repositoryId: deploymentId,
                branchId: computeInstance.attached_branch,
                snapshot_comment: bookmarkMessage
            };
            vscode.postMessage({
                action: "CREATE_BOOKMARK",
                data: { bookmarkReq }
            });
        } catch (err: any) {
            setError(err.message || "Failed to create bookmark. Please try again.");
            setIsCreatingBookmark(false);
            vscode.postMessage({ action: "SHOW_ERROR_MESSAGE", data: { message: err.message || "Failed to create bookmark." } });
        }
    }, [bookmarkMessage, vscode]);

    useEffect(() => {
        fetchAllDeployments();
    }, [fetchAllDeployments]);

    useEffect(() => {
        if (selectedDeploymentId) {
            fetchDeployment(selectedDeploymentId);
        }
    }, [selectedDeploymentId, fetchDeployment]);

    useEffect(() => {
        if (deploymentData && deploymentData.clone_id) {
            fetchCompute(deploymentData.id, deploymentData.clone_id);
            fetchAllSnapshots(deploymentData.id);
            fetchAllBranches(deploymentData.id);
        }
    }, [deploymentData, fetchCompute, fetchAllSnapshots, fetchAllBranches]);

    useEffect(() => {
        const dynamicData = getDynamicSnapshots(snapshots);
        setFilteredSnapshots(filterSnapshots(dynamicData));
    }, [snapshots, compute?.attached_branch, getDynamicSnapshots]);

    useEffect(() => {
        const messageListener = (event: MessageEvent) => {
            const message = event.data;
            switch (message.action) {
                case "LOAD_REPOSITORY_DATA":
                    const repositoryData = message.data.repositoryData;
                    setSelectedDeploymentId(repositoryData.id);
                    setLoading(false);
                    break;
                case "UPDATE_ALL_REPOSITORIES":
                    setDeployments(message.data);
                    setLoading(false);
                    break;
                case "UPDATE_ALL_SNAPSHOTS":
                    setSnapshots(message.data);
                    setLoading(false);
                    break;
                case "UPDATE_ALL_BRANCHES":
                    setBranches(message.data);
                    setLoading(false);
                    break;
                case "UPDATE_REPOSITORY":
                    setDeploymentData(message.data);
                    setLoading(false);
                    break;
                case "UPDATE_COMPUTE":
                    setCompute(message.data);
                    setLoading(false);
                    break;
                case "BOOKMARK_CREATED":
                    setIsCreatingBookmark(false);
                    setSnapshots(message.data.snapshots);
                    setBranches(message.data.branches);
                    setBookmarkMessage('');
                    vscode.postMessage({ action: "SHOW_SUCCESS_MESSAGE", data: { message: "Bookmark created successfully!" } });
                    setLoading(false);
                    break;
                case "CONVERT_TO_PERMANENT_AND_BOOKMARK_SUCCESS":
                    setIsCreatingBookmark(false);
                    if (message.data.snapshots) setSnapshots(message.data.snapshots as Bookmarks);
                    if (message.data.branches) setBranches(message.data.branches as Branches);
                    if (message.data.compute) setCompute(message.data.compute as Compute);
                    setBookmarkMessage('');
                    setLoading(false);
                    break;
                case "BRANCH_CHECKED_OUT":
                case "BRANCH_CREATED":
                case "BOOKMARK_CHECKED_OUT":
                    setSnapshots(message.data.snapshots);
                    setBranches(message.data.branches);
                    if (message.data.compute) setCompute(message.data.compute as Compute);
                    setLoading(false);
                    break;
                case 'UPDATED_MAIN_PANEL':
                    const { database, snapshots: panelSnapshots, branches: panelBranches, compute: panelCompute } = message.data;
                    setSelectedDeploymentId(database.id);
                    setDeploymentData(database);
                    if (panelSnapshots) setSnapshots(panelSnapshots);
                    if (panelBranches) setBranches(panelBranches);
                    if (panelCompute) setCompute(panelCompute);
                    setLoading(false);
                    break;
                case "API_ERROR":
                case "CONVERT_TO_PERMANENT_AND_BOOKMARK_ERROR":
                    setError(message.error || "Operation failed.");
                    setIsCreatingBookmark(false);
                    vscode.postMessage({ action: "SHOW_WARNING_LOG", data: { message: message.error || "Operation failed." } });
                    setLoading(false);
                    break;
                default:
            }
        };
        window.addEventListener("message", messageListener);
        return () => {
            window.removeEventListener("message", messageListener);
        };
    }, [selectedDeploymentId, setSelectedDeploymentId, fetchAllSnapshots]);

    const representersSnapshots = getRepresentingSnapshotsForClones(
        branches,
        snapshots,
    );

    const handleSnapshotChange = (snapshotId: string) => {
        setCurrentSnapshotId(snapshotId);
    };

    const handleCloseEphemeralDialog = () => {
        setIsEphemeralBranchDialogVisible(false);
    };

    const handleConvertToPermanentAction = () => {
        if (!selectedDeploymentId || !compute) {
            vscode.postMessage({ action: "SHOW_WARNING_LOG", data: { message: "Cannot convert & bookmark: Critical data missing." } });
            setIsEphemeralBranchDialogVisible(false);
            return;
        }
        if (!bookmarkMessage.trim()) {
            vscode.postMessage({ action: "SHOW_WARNING_LOG", data: { message: "Bookmark message is required to convert and bookmark." } });
            return;
        }
        setIsCreatingBookmark(true);
        const branchDetailsPayload: Partial<Branch> = {
            deployment_id: selectedDeploymentId,
            id: compute.attached_branch
        };
        vscode.postMessage({
            action: "CONVERT_TO_PERMANENT_AND_BOOKMARK",
            data: {
                branchDetails: branchDetailsPayload,
                snapshot_comment: bookmarkMessage
            }
        });
        setIsEphemeralBranchDialogVisible(false);
    };

    const handleInputSubmit = async () => {
        if (!bookmarkMessage.trim()) {
            vscode.postMessage({ action: "SHOW_WARNING_LOG", data: { message: "Bookmark message cannot be empty." } });
            return;
        }
        if (!selectedDeploymentId || !compute) {
            vscode.postMessage({ action: "SHOW_WARNING_LOG", data: { message: "Cannot create bookmark: Repository or Compute context is missing." } });
            return;
        }
        const currentBranch = branches.find(branch => branch.id === compute.attached_branch);

        if (currentBranch && currentBranch.is_ephemeral) {
            setIsEphemeralBranchDialogVisible(true);
        } else {
            createBookmark(selectedDeploymentId, compute);
        }
    };

    useEffect(() => {
        if ((window as any).imageSrc) {
            setImageSrc((window as any).imageSrc as string);
        }
    }, []);


    return (
        <div className="space-y-6 py-3 bg-vscode-background text-vscode-foreground" data-vscode-context='{"preventDefaultContextMenuItems": true}'>
            {loading && <Loader />}
            {!selectedDeploymentId ?
                <>
                    <div className="flex flex-col justify-center items-center m-auto">
                        {imageSrc && (
                            <img
                                width="40%"
                                height="auto"
                                src={imageSrc}
                                alt="Guepard Logo"
                                className="mb-4 max-w-xs"
                            />
                        )}
                        <h1 className="text-lg font-bold text-center text-vscode-foreground">Please Select a Repository to get started</h1>
                    </div>
                </>
                :
                <>
                    <div className="p-4">
                        <h1 className="text-lg font-bold text-vscode-foreground">Database history graph</h1>
                        <p className="text-vscode-foreground">Navigate through time with ease: visualize, explore, and restore your database seamlessly using our interactive time travel graph</p>
                    </div>
                    <div className="flex flex-row items-center justify-center gap-4 px-4">
                        <div className="text-vscode-foreground">
                            <p className="font-bold">{branches.slice().filter((branch) => !branch.is_ephemeral).length} Branches</p>
                        </div>
                        <div className="flex-grow">
                            <VscodeTextfield
                                placeholder="Bookmark message, e.g. add customers data"
                                value={bookmarkMessage}
                                onInput={(e: Event) => setBookmarkMessage((e.target as HTMLInputElement).value)}
                                className="w-full"
                            />
                        </div>
                        <div>
                            <VscodeButton
                                disabled={isCreatingBookmark || !bookmarkMessage.trim()}
                                onClick={handleInputSubmit}
                                type="submit"
                            >
                                <div className="flex gap-2 items-center py-1">
                                    {isCreatingBookmark ? (
                                        <Loader2 className="animate-spin h-4 w-4" />
                                    ) : (
                                        <BookmarkIcon className="h-4 w-4" />
                                    )}
                                    <p className="font-semibold">Bookmark</p>
                                </div>
                            </VscodeButton>
                        </div>
                    </div>

                    <PopupWindow
                        isOpen={isEphemeralBranchDialogVisible}
                        onClose={handleCloseEphemeralDialog}
                        title="Bookmark on Ephemeral Branch"
                        description="This action requires creating a new branch. Would you like to proceed?"
                        cancelText="Cancel"
                        confirmText="Bookmark Anyway"
                        onConfirm={handleConvertToPermanentAction}
                        isConfirmDisabled={isCreatingBookmark}
                    />
                    <div className="flex">
                        <div className="flex font-bold w-full">
                            <div className="flex flex-col">
                                <VscodeTable bordered-columns zebra responsive>
                                    <VscodeTableHeader>
                                        <VscodeTableRow style={{ height: "30px" }}>
                                            <VscodeTableHeaderCell style={{ height: "30px" }}>
                                                Compute
                                            </VscodeTableHeaderCell>
                                        </VscodeTableRow>
                                    </VscodeTableHeader>
                                </VscodeTable>
                                <div className="flex relative px-4" style={{ width: '200px', minWidth: '100px' }}>
                                    <ComputeSection
                                        deploymentId={selectedDeploymentId}
                                        representingSnapshots={representersSnapshots}
                                        rowHeight={30}
                                        bookmarks={filteredSnapshots}
                                        computeData={compute}
                                    />
                                </div>
                            </div>

                            <div className="flex flex-col">
                                <VscodeTable bordered-columns zebra responsive>
                                    <VscodeTableHeader>
                                        <VscodeTableRow style={{ height: "30px" }}>
                                            <VscodeTableHeaderCell style={{ height: "30px" }}>
                                                Graph
                                            </VscodeTableHeaderCell>
                                        </VscodeTableRow>
                                    </VscodeTableHeader>
                                </VscodeTable>
                                <div className="flex" style={{ width: '200px', minWidth: '200px' }}>
                                    <SnapshotGraph
                                        commits={filteredSnapshots}
                                        width={40}
                                        height={rowHeight * filteredSnapshots.length}
                                        backgroundWidth={treeWidth}
                                        margin={{ top: 15, right: treeWidth, bottom: 0, left: 100 }}
                                        colors={d3.schemeTableau10}
                                        branchConfig={{
                                            ...defaultBranchConfig,
                                            branchSorting,
                                            showVerticalBar,
                                            pathWidth,
                                            spacing,
                                            directionV: 'BT',
                                            directionH: 'LR'
                                        }}
                                        commitConfig={{
                                            ...defaultCommitConfig,
                                            enableAnimations,
                                            dotRadius,
                                        }}
                                        tooltipConfig={tooltipConfig}
                                        selectedCloneId={compute?.attached_branch}
                                        rowHeight={rowHeight}
                                        onBranchColorsChange={setBranchColors}
                                        currentSnapshotId={currentSnapshotId}
                                    />
                                    <div
                                        className="transparent w-1 cursor-ew-resize"
                                        style={{ zIndex: 10, marginTop: "15px" }}
                                    >
                                        <BorderGraph
                                            commits={filteredSnapshots}
                                            width={10}
                                            rowHeight={rowHeight}
                                            branchColors={branchColors}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <SnapshotTable
                            key={`snapshots-table-${filteredSnapshots.length}-${branches.length}`}
                            snapshots={filteredSnapshots}
                            branches={branches}
                            branchColors={branchColors}
                            onSnapshotChange={handleSnapshotChange}
                            selectedDeploymentId={selectedDeploymentId}
                            representersSnapshots={representersSnapshots}
                            selectedCloneId={compute?.attached_branch}
                        />
                    </div>
                </>
            }
        </div>
    );
}

export default MainPanel;
