import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import {
    VscodeIcon,
    VscodeTable,
    VscodeTableBody,
    VscodeTableCell,
    VscodeTableHeader,
    VscodeTableHeaderCell,
    VscodeTableRow,
    VscodeButton,
    VscodeTextfield,
} from "@vscode-elements/react-elements";
import { Branch, Branches, CheckoutBranchRequest, CreateBranchRequest } from "@/ui/types/branch.type";
import Badge from "../../../components/Badge";
import ContextMenu from "../../../components/ContextMenu";
import VSCodeApiService from "@/services/vscode-api-service";
import PopupWindow from "./popupWindow";
import { Bookmark, Bookmarks } from "@/ui/types/bookmark.type";

interface SnapshotTableProps {
    snapshots: Bookmarks;
    branches: Branches;
    branchColors: Record<string, string>;
    selectedDeploymentId: string | null;
    representersSnapshots: Array<{ clone: Branch; representingSnapshot: Bookmark | undefined }>;
    onSnapshotChange: (snapshotId: string) => void;
    selectedCloneId: string | undefined;
}

interface MenuItem {
    label: string;
    action: () => void;
    disabled?: boolean;
    isSeparator?: boolean;
}

const SnapshotTable: React.FC<SnapshotTableProps> = ({
    snapshots,
    branches,
    branchColors,
    onSnapshotChange,
    selectedDeploymentId,
    representersSnapshots,
    selectedCloneId,
}) => {
    const vscode = VSCodeApiService.getInstance();
    const [contextMenuBranchId, setContextMenuBranchId] = useState<string | null>(null);
    const [contextMenuSnapshotId, setContextMenuSnapshotId] = useState<string | null>(null);
    const [contextMenuSnapshotName, setContextMenuSnapshotName] = useState<string | null>(null);
    const [isPopupOpen, setIsPopupOpen] = useState(false);
    const [isDiscardChangesChecked, setIsDiscardChangesChecked] = useState(false);
    const [isCheckoutChecked, setCheckoutChecked] = useState(false);
    const [popupTitle, setPopupTitle] = useState<string>("");
    const [popupDescription, setPopupDescription] = useState<string>("");
    const [popupCheckboxLabel, setPopupCheckboxLabel] = useState<string>("");
    const [popupConfirmText, setPopupConfirmText] = useState<string>("");
    const [popupNeedsBranchNameInput, setPopupNeedsBranchNameInput] = useState<boolean>(false);
    const [popupBranchName, setPopupBranchName] = useState<string>("");
    const [createBranchInProgress, setCreateBranchInProgress] = useState<string | null>(null);
    const [actionMenuOpenForRow, setActionMenuOpenForRow] = useState<string | null>(null);
    const [isCheckoutingBranch, setIsCheckoutingBranch] = useState<string | null>(null);

    const baseContextMenuText = {
        checkoutBookmark: {
            title: (snapshotName: string) => `Checkout this version "${snapshotName}"?`,
            description: "You may have uncommitted changes. These changes could be lost if you discard them.",
            checkboxLabel: "Discard changes?",
            confirmText: "Checkout",
        },
        createBranch: {
            title: (snapshotName: string) => `Create a branch at bookmark "${snapshotName}"?`,
            description: "Enter a name for the new branch.",
            checkboxLabel: "Checkout after creating?",
            confirmText: "Create Branch",
        },
        discardChanges: {
            title: (workspaceName: string) => `Discard uncommitted changes in workspace "${workspaceName}"?`,
            description: "This action cannot be undone and will revert the workspace to the last bookmark. All uncommitted changes will be lost.",
            checkboxLabel: "",
            confirmText: "Discard Changes",
        }
    };

    useEffect(() => {
        const representer = representersSnapshots.find(
            (rs) => rs.clone.id === selectedCloneId
        );
        if (representer && representer.representingSnapshot) {
            onSnapshotChange(representer.representingSnapshot.id);
        } else if (snapshots.length > 0) {
            onSnapshotChange(snapshots[snapshots.length - 1].id);
        } else {
            onSnapshotChange("");
        }
    }, [selectedCloneId, representersSnapshots, onSnapshotChange, snapshots]);


    const closePopup = () => {
        setIsPopupOpen(false);
        setContextMenuSnapshotId(null);
        setContextMenuBranchId(null);
        setContextMenuSnapshotName(null);
        setPopupNeedsBranchNameInput(false);
        setPopupBranchName("");
        setIsDiscardChangesChecked(false);
        setCheckoutChecked(false);
    };

    const handlePopupConfirm = () => {
        if (!selectedDeploymentId) return;

        if (popupTitle.startsWith("Discard uncommitted changes")) {
            if (contextMenuBranchId) { // Should be selectedCloneId
                vscode.postMessage({
                    action: "DISCARD_CHANGES",
                    data: {
                        repositoryId: selectedDeploymentId,
                        branchId: contextMenuBranchId,
                    }
                });
            }
        } else if (popupTitle.startsWith("Checkout this version")) {
            if (contextMenuSnapshotId && contextMenuBranchId) {
                handleCheckoutBookmark(contextMenuSnapshotId, contextMenuBranchId);
            }
        } else if (popupTitle.startsWith("Create a branch")) {
            if (contextMenuSnapshotId && contextMenuBranchId) {
                if (!popupBranchName.trim()) {
                    vscode.postMessage({ action: 'SHOW_ERROR_MESSAGE', data: { message: 'Branch name cannot be empty.' } });
                    return;
                }
                handleCreateBranch(contextMenuSnapshotId, contextMenuBranchId, popupBranchName.trim());
            }
        }
        closePopup();
    };

    const handleCheckboxChange = (isChecked: boolean) => {
        if (popupTitle.startsWith("Checkout this version")) {
            setIsDiscardChangesChecked(isChecked);
        } else if (popupTitle.startsWith("Create a branch")) {
            setCheckoutChecked(isChecked);
        }
    };

    const handleCheckoutBranch = (targetBranchId: string) => {
        try {
            if (selectedDeploymentId && targetBranchId) {
                if (selectedCloneId === targetBranchId) {
                    vscode.postMessage({ action: 'SHOW_WARNING_LOG', data: { message: 'You cannot checkout workspace compute.' } });
                    return;
                }
                setIsCheckoutingBranch(targetBranchId);
                const checkoutBranchRequest: CheckoutBranchRequest = {
                    repositoryId: selectedDeploymentId,
                    branchId: targetBranchId,
                };
                vscode.postMessage({
                    action: "CHECKOUT_BRANCH",
                    data: { checkoutBranchRequest }
                });
            }
        } catch (err) {
            setIsCheckoutingBranch(null);
            vscode.postMessage({ action: 'API_ERROR', data: { message: 'Failed to checkout branch.' } });
        }
    };

    const handleCheckoutBookmark = (snapshotId: string, originalBranchId: string) => {
        try {
            if (selectedDeploymentId) {
                setIsCheckoutingBranch(originalBranchId);
                let bookmarkCheckoutReq: CreateBranchRequest = {
                    repositoryId: selectedDeploymentId,
                    branchId: originalBranchId,
                    bookmarkId: snapshotId,
                    discard_changes: isDiscardChangesChecked ? "true" : "false",
                    checkout: true,
                    ephemeral: true
                };
                vscode.postMessage({
                    action: "CHECKOUT_BOOKMARK",
                    data: { bookmarkCheckoutReq }
                });
            }
        } catch (err) {
            setIsCheckoutingBranch(null);
            vscode.postMessage({ action: 'API_ERROR', data: { message: 'Failed to checkout bookmark.' } });
        }
    };

    const handleCreateBranch = (snapshotId: string, originalBranchId: string, newBranchName: string) => {
        try {
            if (selectedDeploymentId) {
                setCreateBranchInProgress(newBranchName);
                let createBranchReq: CreateBranchRequest = {
                    repositoryId: selectedDeploymentId,
                    branchId: originalBranchId,
                    bookmarkId: snapshotId,
                    discard_changes: "true",
                    checkout: isCheckoutChecked,
                    ephemeral: false,
                    branch_name: newBranchName
                };
                vscode.postMessage({
                    action: "CREATE_BRANCH",
                    data: { createBranchReq }
                });
            }
        } catch (err) {
            setCreateBranchInProgress(null);
            vscode.postMessage({ action: 'API_ERROR', data: { message: 'Failed to create branch.' } });
        }
    };

    useEffect(() => {
        const handleCommand = (event: MessageEvent) => {
            const message = event.data;
            if (message.action === "BRANCH_CHECKED_OUT" || message.action === "BOOKMARK_CHECKED_OUT" || message.action === "CHANGES_DISCARDED") {
                setIsCheckoutingBranch(null);
            }
            if (message.action === "BRANCH_CREATED") {
                setCreateBranchInProgress(null);
            }
        };
        window.addEventListener('message', handleCommand);
        return () => {
            window.removeEventListener('message', handleCommand);
        };
    }, []);

    const getMenuItemsForRow = (snapshot: Bookmark, isHeadSnapshot: boolean): MenuItem[] => {
        let items: MenuItem[] = [];
        const representingClonesForRow = representersSnapshots
            .filter(rs => rs.representingSnapshot?.id === snapshot.id)
            .map(rs => rs.clone);

        if (isHeadSnapshot) {
            items.push({
                label: "Discard Uncommitted Changes",
                action: () => {
                    setContextMenuBranchId(selectedCloneId);
                    const currentBranch = branches.find(b => b.id === selectedCloneId);
                    const workspaceName = currentBranch?.branch_name || currentBranch?.name || selectedCloneId;
                    setPopupTitle(baseContextMenuText.discardChanges.title(workspaceName));
                    setPopupDescription(baseContextMenuText.discardChanges.description);
                    setPopupCheckboxLabel(baseContextMenuText.discardChanges.checkboxLabel);
                    setPopupConfirmText(baseContextMenuText.discardChanges.confirmText);
                    setPopupNeedsBranchNameInput(false);
                    setIsPopupOpen(true);
                },
                disabled: isCheckoutingBranch === selectedCloneId
            });
        } else {
            const hasTargetableRepresentingClones = representingClonesForRow.some(clone => !clone.is_ephemeral);

            if (representingClonesForRow.length > 0) {
                representingClonesForRow
                    .filter(clone => !clone.is_ephemeral)
                    .forEach(clone => {
                        items.push({
                            label: `Checkout Branch: ${clone.branch_name || clone.name}`,
                            action: () => handleCheckoutBranch(clone.id),
                            disabled: isCheckoutingBranch === clone.id || selectedCloneId === clone.id
                        });
                    });
            }

            if (!hasTargetableRepresentingClones || items.length === 0) {
                items.push({
                    label: "Checkout Bookmark",
                    action: () => {
                        setContextMenuSnapshotId(snapshot.id);
                        setContextMenuBranchId(snapshot.dataset_id);
                        setContextMenuSnapshotName(snapshot.name);
                        setPopupTitle(baseContextMenuText.checkoutBookmark.title(snapshot.name));
                        setPopupDescription(baseContextMenuText.checkoutBookmark.description);
                        setPopupCheckboxLabel(baseContextMenuText.checkoutBookmark.checkboxLabel);
                        setPopupConfirmText(baseContextMenuText.checkoutBookmark.confirmText);
                        setPopupNeedsBranchNameInput(false);
                        setIsPopupOpen(true);
                    },
                    disabled: isCheckoutingBranch === snapshot.dataset_id
                });
            }


            items.push({
                label: "Create Branch from Bookmark",
                action: () => {
                    setContextMenuSnapshotId(snapshot.id);
                    setContextMenuBranchId(snapshot.dataset_id);
                    setContextMenuSnapshotName(snapshot.name);
                    setPopupTitle(baseContextMenuText.createBranch.title(snapshot.name));
                    setPopupDescription(baseContextMenuText.createBranch.description);
                    setPopupCheckboxLabel(baseContextMenuText.createBranch.checkboxLabel);
                    setPopupConfirmText(baseContextMenuText.createBranch.confirmText);
                    setPopupNeedsBranchNameInput(true);
                    setPopupBranchName("");
                    setIsPopupOpen(true);
                },
                disabled: !!createBranchInProgress
            });
        }
        return items;
    };

    return (
        <>
            <VscodeTable zebra responsive borderedColumns resizable>
                <VscodeTableHeader>
                    <VscodeTableRow style={{ height: "30px" }}>
                        <VscodeTableHeaderCell style={{ height: "30px" }}>Description</VscodeTableHeaderCell>
                        <VscodeTableHeaderCell style={{ height: "30px" }}>Author</VscodeTableHeaderCell>
                        <VscodeTableHeaderCell style={{ height: "30px" }}>Date</VscodeTableHeaderCell>
                        <VscodeTableHeaderCell style={{ height: "30px" }}>Hash</VscodeTableHeaderCell>
                        <VscodeTableHeaderCell style={{ height: "30px" }}>Actions</VscodeTableHeaderCell>
                    </VscodeTableRow>
                </VscodeTableHeader>

                {selectedDeploymentId && (
                    <VscodeTableBody>
                        {snapshots.slice().reverse().map((snapshot, index) => {
                            const isHead = snapshot.snapshot_type === "HEAD" || (index === 0 && !snapshots.some(s => s.snapshot_type === "HEAD"));
                            const menuItemsForThisRow = getMenuItemsForRow(snapshot, isHead);
                            const representingClones = representersSnapshots
                                .filter(rs => rs.representingSnapshot?.id === snapshot.id)
                                .map(rs => rs.clone);

                            return (
                                <VscodeTableRow key={snapshot.id} style={{ height: "30px" }}>
                                    <VscodeTableCell style={{ height: "30px" }}>
                                        <div className="flex items-center gap-3" style={{ margin: '2px 0' }}>
                                            <div className="relative" style={{ display: 'inline-block' }}>
                                                <div className="flex items-center space-x-2" style={{ marginLeft: 10 }}>
                                                    {representingClones
                                                        .filter(clone => !clone.is_ephemeral || clone.id === selectedCloneId)
                                                        .slice()
                                                        .sort((a, b) => a.id === selectedCloneId ? -1 : b.id === selectedCloneId ? 1 : 0)
                                                        .map((clone) => (
                                                            <div key={clone.id} className='space-x-2 flex items-center relative'>
                                                                {clone.id === selectedCloneId && (
                                                                    <div
                                                                        className='hidden md:block compute-position'
                                                                        style={{
                                                                            width: "12px", height: "12px", borderRadius: "50%",
                                                                            border: `2px solid ${branchColors[selectedCloneId] || "#ccc"}`,
                                                                        }}
                                                                    />
                                                                )}
                                                                {!clone.is_ephemeral && (
                                                                    <Badge
                                                                        color={clone.id === selectedCloneId ? '#fff' : branchColors[clone.id] || '#ccc'}
                                                                        bgColor={clone.id === selectedCloneId ? branchColors[clone.id] || '#ccc' : 'transparent'}
                                                                        borderColor={clone.id === selectedCloneId ? "transparent" : branchColors[clone.id] || '#ccc'}
                                                                        isLoading={isCheckoutingBranch === clone.id || createBranchInProgress === clone.id}
                                                                        icon="gitBranch"
                                                                    >
                                                                        {clone.branch_name || clone.name}
                                                                    </Badge>
                                                                )}
                                                            </div>
                                                        ))}
                                                    <div
                                                        className={`truncate text-sm font-medium max-w-[29ch] ${isHead ? "text-gray-500" : ""} ${snapshot.snapshot_comment === 'Initial Bookmark' ? 'snapshot' : 'wip'}`}
                                                        style={{ textAlign: "left", marginLeft: '8px' }}
                                                        title={snapshot.snapshot_comment}
                                                    >
                                                        {snapshot.snapshot_comment}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </VscodeTableCell>
                                    <VscodeTableCell style={{ height: "30px" }}>{snapshot.created_by}</VscodeTableCell>
                                    <VscodeTableCell style={{ height: "30px" }}>
                                        {new Date(snapshot.created_date).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' })}
                                        {new Date(snapshot.created_date).toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' })}
                                    </VscodeTableCell>
                                    <VscodeTableCell style={{ height: "30px" }}>{snapshot.name}</VscodeTableCell>
                                    <VscodeTableCell style={{ height: "30px" }}>
                                        {menuItemsForThisRow.length > 0 && (
                                            <ContextMenu
                                                menuItems={menuItemsForThisRow}
                                                isOpen={actionMenuOpenForRow === snapshot.id}
                                                setIsOpen={(state) => {
                                                    setActionMenuOpenForRow(state ? snapshot.id : null);
                                                }}
                                            >
                                                <div
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        setActionMenuOpenForRow(snapshot.id);
                                                    }}
                                                    style={{ cursor: 'pointer', display: 'inline-flex', alignItems: 'center', padding: '0 4px' }}
                                                    title="Actions"
                                                >
                                                    <VscodeIcon name="kebab-vertical" size={16} />
                                                </div>
                                            </ContextMenu>
                                        )}
                                    </VscodeTableCell>
                                </VscodeTableRow>
                            );
                        })}
                    </VscodeTableBody>
                )}
            </VscodeTable>
            <PopupWindow
                isOpen={isPopupOpen}
                onClose={closePopup}
                title={popupTitle}
                description={popupDescription}
                cancelText="Cancel"
                confirmText={popupConfirmText}
                onConfirm={handlePopupConfirm}
                onCheckboxChange={handleCheckboxChange}
                checkboxLabel={popupCheckboxLabel}
                showBranchNameInput={popupNeedsBranchNameInput}
                branchNameValue={popupBranchName}
                onBranchNameChange={setPopupBranchName}
                isConfirmDisabled={popupNeedsBranchNameInput && !popupBranchName.trim()}
            />
        </>
    );
};

export default SnapshotTable;