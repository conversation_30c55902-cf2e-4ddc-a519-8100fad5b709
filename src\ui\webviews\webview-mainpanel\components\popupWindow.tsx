import { VscodeButton, VscodeCheckbox, VscodeTextfield } from "@vscode-elements/react-elements";
import { useState, useEffect } from "react";

interface PopupWindowProps {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    description: string;
    cancelText: string;
    confirmText: string;
    onConfirm: () => void;
    onCheckboxChange?: (checked: boolean) => void;
    checkboxLabel?: string;
    showBranchNameInput?: boolean;
    branchNameValue?: string;
    onBranchNameChange?: (value: string) => void;
    isConfirmDisabled?: boolean;
}

export default function PopupWindow({
    isOpen,
    onClose,
    title,
    description,
    cancelText,
    confirmText,
    onConfirm,
    onCheckboxChange,
    checkboxLabel,
    showBranchNameInput = false,
    branchNameValue = "",
    onBranchNameChange,
    isConfirmDisabled = false,
}: PopupWindowProps) {
    const [isChecked, setIsChecked] = useState(false);

    useEffect(() => {
        if (isOpen) {
            setIsChecked(false);
        }
    }, [isOpen]);

    const handleLocalCheckboxChange = (event: Event) => {
        const checkbox = event.target as HTMLInputElement;
        const newValue = checkbox.checked;
        setIsChecked(newValue);
        if (onCheckboxChange) {
            onCheckboxChange(newValue);
        }
    };

    const handleBranchNameInputChange = (event: Event) => {
        const inputElement = event.target as HTMLInputElement;
        if (onBranchNameChange) {
            onBranchNameChange(inputElement.value);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm bg-black bg-opacity-30">
            <div className="bg-vscode-widgetBackground text-vscode-foreground border border-vscode-widgetBorder max-w-md w-full p-6 rounded-lg shadow-xl m-4">
                <h2 className="text-lg font-bold mb-3 text-vscode-foreground">{title}</h2>
                <p className="text-sm mt-2 mb-4 text-vscode-foreground">{description}</p>

                {showBranchNameInput && (
                    <div className="mb-4">
                        <label htmlFor="branchNameInputPopup" className="block text-sm font-medium mb-1 text-vscode-foreground">Branch Name:</label>
                        <VscodeTextfield
                            id="branchNameInputPopup"
                            value={branchNameValue}
                            onInput={handleBranchNameInputChange}
                            placeholder="Enter new branch name"
                            className="w-full"
                        >
                        </VscodeTextfield>
                    </div>
                )}

                {checkboxLabel && (
                    <div className="flex items-center mt-4 mb-4 space-x-2">
                        <VscodeCheckbox
                            checked={isChecked}
                            onChange={handleLocalCheckboxChange}
                            id="popupCheckbox"
                        >
                            <span className="text-vscode-foreground">{checkboxLabel}</span>
                        </VscodeCheckbox>
                    </div>
                )}
                <div className="flex justify-end mt-6 space-x-3">
                    <VscodeButton
                        onClick={onClose}
                        secondary
                        type="button"
                    >
                        {cancelText}
                    </VscodeButton>
                    <VscodeButton
                        onClick={onConfirm}
                        disabled={isConfirmDisabled}
                        type="button"
                    >
                        {confirmText}
                    </VscodeButton>
                </div>
            </div>
        </div>
    );
}