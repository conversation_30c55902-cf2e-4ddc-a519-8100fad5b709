import React, { useEffect, useState } from "react";
import { LaptopMinimal, Loader2 } from "lucide-react";
import Badge from "../../../components/Badge";
import { Bookmark, Bookmarks } from "@/ui/types/bookmark.type";
import { Branch } from "@/ui/types/branch.type";
import VSCodeApiService from "@/services/vscode-api-service";
import { Compute } from "@/ui/types/compute.type";

interface ComputeSectionProps {
  deploymentId: string | null;
  representingSnapshots: Array<{ clone: Branch; representingSnapshot: Bookmark | undefined }>;
  rowHeight: number;
  bookmarks: Bookmarks;
  computeData: Compute | null;
};

export const ComputeSection: React.FC<ComputeSectionProps> = ({
  deploymentId,
  representingSnapshots,
  rowHeight,
  bookmarks,
  computeData
}) => {
  const vscode = VSCodeApiService.getInstance();

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const representerSnapshot = representingSnapshots.find(
    (item) => item.clone.id === computeData?.attached_branch
  );

  if (!representerSnapshot) return null;

  const snapshotIndex = bookmarks
    .slice()
    .reverse()
    .findIndex((item) => item.id === representerSnapshot.representingSnapshot?.id);

  if (snapshotIndex === -1) return null;

  return (
    <div
      style={{
        position: "absolute",
        top: `${snapshotIndex * rowHeight + 5}px`,
      }}
    >
      <Badge bgColor="#ffcb51" borderColor="transparent" color="#000000" icon="laptop" >
        {computeData?.name || "free-compute"}
      </Badge>
    </div>
  );
};

export default ComputeSection;