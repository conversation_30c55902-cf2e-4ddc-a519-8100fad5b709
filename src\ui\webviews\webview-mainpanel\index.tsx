import * as React from 'react';
import { createRoot } from 'react-dom/client';
import MainPanel from '@/ui/webviews/webview-mainpanel/components/MainPanel';

declare global {
    namespace JSX {
        interface IntrinsicElements {
            'vscode-dev-toolbar': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
        }
    }
    interface Window {
        imageSrc?: string;
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('root');
    if (container) {
        const root = createRoot(container);
        root.render(
            <React.StrictMode>
                {<vscode-dev-toolbar></vscode-dev-toolbar>}
                <MainPanel />
            </React.StrictMode>
        );
    }
});