import { WebviewPanel, Webview, Uri, ViewColumn, window, EventEmitter, ExtensionContext, Event } from "vscode";
import { Utils } from "@/ui/utils/utils";
import path from "path";
import { CreateBookmarkRequest } from "@/ui/types/bookmark.type";
import { getRepositories, getRepository } from "@/server/actions/repository-server-actions";
import { checkoutBookmark, createBookmark, getAllSnapshots, getSnapshots } from "@/server/actions/bookmark-server-actions";
import { getCompute } from "@/server/actions/compute-server-actions";
import { checkoutBranch, createBranch, getBranch, getBranches, updateBranch } from "@/server/actions/branch-server-actions";
import { Branch, CheckoutBranchRequest, CreateBranchRequest } from "@/ui/types/branch.type";
import { Repository } from "@/ui/types/repository.type";

type WebviewMessage =
    | { action: "SHOW_WARNING_LOG"; data: { message: string } }
    | { action: "GET_ALL_REPOSITORIES"; data: {} }
    | { action: "GET_REPOSITORY"; data: { deploymentId: string } }
    | { action: "GET_SNAPSHOTS"; data: { deploymentId: string, clone_id: string } }
    | { action: "GET_ALL_SNAPSHOTS"; data: { deploymentId: string } }
    | { action: "CREATE_BOOKMARK"; data: { bookmarkReq: CreateBookmarkRequest } }
    | { action: "CHECKOUT_BOOKMARK"; data: { bookmarkCheckoutReq: CreateBranchRequest } }
    | { action: "CREATE_BRANCH"; data: { createBranchReq: CreateBranchRequest } }
    | { action: "GET_ALL_BRANCHES"; data: { deploymentId: string } }
    | { action: "GET_BRANCH"; data: { deploymentId: string, cloneId: string } }
    | { action: "CHECKOUT_BRANCH"; data: { checkoutBranchRequest: CheckoutBranchRequest } }
    | { action: "GET_COMPUTE"; data: { deploymentId: string, cloneId: string } }
    | { action: "UPDATE_MAIN_PANEL"; data: { db: Repository } }
    | { action: "CONVERT_TO_PERMANENT_AND_BOOKMARK"; data: { branchDetails: Partial<Branch>, snapshot_comment: string } }
    | { action: "API_ERROR"; error: string }
    | { action: "CONVERT_TO_PERMANENT_AND_BOOKMARK_ERROR"; error: string };

export class WebviewPanelProvider {
    private panel: WebviewPanel | undefined;
    private _extensionPath: Uri;
    private context: ExtensionContext;
    private messageHandlers: Map<string, (message: any, webview: Webview) => void> = new Map();
    private data: any[] = [];
    private onDidChangeTreeData: EventEmitter<any | undefined | null | void> = new EventEmitter();
    private previousDatabaseState: Repository | null = null;
    private stateCheckInterval: ReturnType<typeof setInterval> | undefined;
    private onStatusBarUpdateRequested: EventEmitter<void> = new EventEmitter<void>();

    constructor(private readonly extensionPath: Uri, context: ExtensionContext) {
        this._extensionPath = extensionPath;
        this.context = context;
        this.registerDefaultHandlers();

        this.previousDatabaseState = this.context.globalState.get<Repository>('selectedRepoData') || null;
        this.startStateMonitoring();
    }

    private startStateMonitoring() {
        if (this.stateCheckInterval) {
            clearInterval(this.stateCheckInterval);
        }

        this.stateCheckInterval = setInterval(() => this.checkForGlobalStateChanges(), 1000);

        setTimeout(() => this.checkForGlobalStateChanges(), 500);
    }

    private stopStateMonitoring() {
        if (this.stateCheckInterval) {
            clearInterval(this.stateCheckInterval);
            this.stateCheckInterval = undefined;
        }
    }

    private async checkForGlobalStateChanges() {
        try {
            const currentDatabase = this.context.globalState.get<Repository>('selectedRepoData');

            if (JSON.stringify(currentDatabase) !== JSON.stringify(this.previousDatabaseState)) {
                this.previousDatabaseState = currentDatabase || null;

                if (currentDatabase) {
                    await this.handleDatabaseSelection();
                }

                this.onStatusBarUpdateRequested.fire();
            }
        } catch (error) {
            console.error('Error checking for global state changes:', error);
        }
    }

    public get onStatusBarUpdate(): Event<void> {
        return this.onStatusBarUpdateRequested.event;
    }

    registerMessageHandler(action: string, handler: (message: any, webview: Webview) => void) {
        this.messageHandlers.set(action, handler);
    }

    private registerDefaultHandlers() {
        this.registerMessageHandler("GET_ALL_REPOSITORIES", async (message, webview) => {
            try {
                const repositories = await getRepositories();
                webview.postMessage({ action: "UPDATE_ALL_REPOSITORIES", data: repositories });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET repositories: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("GET_REPOSITORY", async (message, webview) => {
            try {
                const repository = await getRepository(message.data.deploymentId);
                webview.postMessage({ action: "UPDATE_REPOSITORY", data: repository });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET repository: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("GET_ALL_SNAPSHOTS", async (message, webview) => {
            try {
                const snapshots = await getAllSnapshots(message.data.deploymentId);
                webview.postMessage({ action: "UPDATE_ALL_SNAPSHOTS", data: snapshots });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET snapshots: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("GET_SNAPSHOTS", async (message, webview) => {
            try {
                const snapshot = await getSnapshots(message.data.deploymentId, message.data.cloneId);
                webview.postMessage({ action: "UPDATE_SNAPSHOTS", data: snapshot });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET clone snapshots: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("GET_ALL_BRANCHES", async (message, webview) => {
            try {
                const branches = await getBranches(message.data.deploymentId);
                webview.postMessage({ action: "UPDATE_ALL_BRANCHES", data: branches });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET all branches: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("GET_BRANCH", async (message, webview) => {
            try {
                const branchData = await getBranch(message.data.deploymentId, message.data.cloneId);
                webview.postMessage({ action: "UPDATE_BRANCH", data: branchData });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET branch: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("GET_COMPUTE", async (message, webview) => {
            try {
                const compute = await getCompute(message.data.deploymentId, message.data.cloneId);
                webview.postMessage({ action: "UPDATE_COMPUTE", data: compute });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET compute: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("CREATE_BOOKMARK", async (message, webview) => {
            try {
                const bookmarkRequest: CreateBookmarkRequest = message.data.bookmarkReq;
                const result = await createBookmark(bookmarkRequest);
                const snapshots = await getAllSnapshots(bookmarkRequest.repositoryId);
                const branches = await getBranches(bookmarkRequest.repositoryId);

                webview.postMessage({
                    action: "BOOKMARK_CREATED",
                    data: { result, snapshots, branches }
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to create bookmark: ${errorMessage}`,
                });
            }
        });

        this.registerMessageHandler("CONVERT_TO_PERMANENT_AND_BOOKMARK", async (message, webview) => {
            const { branchDetails, snapshot_comment } = message.data;
            const repositoryId = branchDetails.deployment_id;
            const branchId = branchDetails.id;

            if (!repositoryId || !branchId) {
                webview.postMessage({
                    action: "CONVERT_TO_PERMANENT_AND_BOOKMARK_ERROR",
                    error: "Missing repositoryId or branchId for converting branch.",
                });
                return;
            }

            try {
                const updatePayload: Partial<Branch> = { ...branchDetails, is_ephemeral: false };
                const updateResult = await updateBranch(updatePayload);

                if (!updateResult || !updateResult.success) {
                    throw new Error(updateResult?.error || "Failed to update branch to permanent.");
                }

                const bookmarkRequest: CreateBookmarkRequest = {
                    repositoryId: repositoryId,
                    branchId: branchId,
                    snapshot_comment: snapshot_comment
                };
                const bookmarkResult = await createBookmark(bookmarkRequest);

                const newSnapshots = await getAllSnapshots(repositoryId);
                const newBranches = await getBranches(repositoryId);
                const newCompute = await getCompute(repositoryId, branchId);

                webview.postMessage({
                    action: "CONVERT_TO_PERMANENT_AND_BOOKMARK_SUCCESS",
                    data: {
                        bookmarkResult,
                        snapshots: newSnapshots,
                        branches: newBranches,
                        compute: newCompute
                    }
                });

            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Unknown error during convert and bookmark.";
                webview.postMessage({
                    action: "CONVERT_TO_PERMANENT_AND_BOOKMARK_ERROR",
                    error: `Failed to convert to permanent and bookmark: ${errorMessage}`,
                });
            }
        });

        this.registerMessageHandler("CREATE_BRANCH", async (message, webview) => {
            try {
                const branchRequest: CreateBranchRequest = message.data.createBranchReq;
                const result = await createBranch(branchRequest);
                const snapshots = await getAllSnapshots(branchRequest.repositoryId);
                const branches = await getBranches(branchRequest.repositoryId);

                webview.postMessage({
                    action: "BRANCH_CREATED",
                    data: { result, snapshots, branches }
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to create branch: ${errorMessage}`,
                });
            }
        });

        this.registerMessageHandler("CHECKOUT_BRANCH", async (message, webview) => {
            try {
                const checkoutBranchRequest: CheckoutBranchRequest = message.data.checkoutBranchRequest;
                const result = await checkoutBranch(checkoutBranchRequest);
                const snapshots = await getAllSnapshots(checkoutBranchRequest.repositoryId);
                const branches = await getBranches(checkoutBranchRequest.repositoryId);
                const compute = await getCompute(checkoutBranchRequest.repositoryId, checkoutBranchRequest.branchId);


                webview.postMessage({
                    action: "BRANCH_CHECKED_OUT",
                    data: { result, snapshots, branches, compute }
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to checkout to branch: ${errorMessage}`,
                });
            }
        });

        this.registerMessageHandler("CHECKOUT_BOOKMARK", async (message, webview) => {
            try {
                const bookmarkCheckoutReq: CreateBranchRequest = message.data.bookmarkCheckoutReq;
                const result = await checkoutBookmark(bookmarkCheckoutReq);
                const snapshots = await getAllSnapshots(bookmarkCheckoutReq.repositoryId);
                const branches = await getBranches(bookmarkCheckoutReq.repositoryId);
                const compute = await getCompute(bookmarkCheckoutReq.repositoryId, result.id || bookmarkCheckoutReq.branchId);


                webview.postMessage({
                    action: "BOOKMARK_CHECKED_OUT",
                    data: { result, snapshots, branches, compute }
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to create branch from bookmark: ${errorMessage}`,
                });
            }
        });
    }

    public refresh() {
        if (this.panel) {
            this.panel.webview.html = this._getHtmlForWebview(this.panel.webview);
        }
    }

    show() {
        if (this.panel) {
            this.panel.reveal(ViewColumn.One);
            return;
        }

        this.panel = window.createWebviewPanel(
            'webviewMainPanel', 'Guepard', ViewColumn.One,
            { enableScripts: true, localResourceRoots: [this.extensionPath], retainContextWhenHidden: true }
        );
        this.activateMessageListener();

        const extensionPathStr = this.extensionPath.fsPath;

        this.panel.iconPath = Uri.file(
            path.join(extensionPathStr, "public", "assets", "guepard-icon.png")
        );

        this.panel.webview.html = this._getHtmlForWebview(this.panel.webview);

        this.panel.onDidDispose(() => {
            this.panel = undefined;
            this.messageHandlers.clear();
            this.stopStateMonitoring();
        });

        const currentDatabase = this.context.globalState.get<Repository>('selectedRepoData');
        if (currentDatabase) {
            setTimeout(() => this.handleDatabaseSelection(), 1000);
        }
    }

    private activateMessageListener() {
        if (!this.panel?.webview) { return; }

        const currentPanel = this.panel;

        currentPanel.webview.onDidReceiveMessage((message: WebviewMessage) => {
            switch (message.action) {
                case 'SHOW_WARNING_LOG':
                    window.showWarningMessage(message.data.message);
                    break;
            }

            const handler = this.messageHandlers.get(message.action);
            if (handler) {
                handler(message, this.panel!.webview);
            } else {
                const knownActions = [
                    'SHOW_WARNING_LOG', 'GET_SNAPSHOTS', 'GET_ALL_SNAPSHOTS',
                    'GET_ALL_REPOSITORIES', 'GET_REPOSITORY', 'GET_ALL_BRANCHES',
                    'GET_BRANCH', 'GET_COMPUTE', 'CREATE_BOOKMARK', 'CHECKOUT_BOOKMARK',
                    'CREATE_BRANCH', 'CHECKOUT_BRANCH', 'UPDATED_MAIN_PANEL',
                    'CONVERT_TO_PERMANENT_AND_BOOKMARK'
                ];
                if (!knownActions.includes(message.action)) {
                    console.warn(`(Main Panel) No handler for action: ${message.action}`);
                }
            }
        });

        this.panel.onDidDispose(() => {
            currentPanel.dispose();
            this.panel = undefined;
            this.messageHandlers.clear();
            this.stopStateMonitoring();
        }, null);
    }

    private async handleDatabaseSelection() {
        try {
            const database = this.context.globalState.get<Repository>('selectedRepoData');
            if (database) {
                const snapshots = await getAllSnapshots(database.id);
                const branches = await getBranches(database.id);
                const compute = database.clone_id ? await getCompute(database.id, database.clone_id) : null;
                if (this.panel) {
                    this.panel.webview.postMessage({
                        action: 'UPDATED_MAIN_PANEL',
                        data: {
                            database,
                            snapshots,
                            branches,
                            compute
                        }
                    });
                }
                window.showInformationMessage(`Opened database: ${database.repository_name}`);
            }
        } catch (error) {
            window.showErrorMessage(`Failed to open database: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    private _getHtmlForWebview(webview: Webview) {

        const imagePath = Uri.file(
            path.join(this.context.extensionPath, "public", "assets", "guepard-logo.png")
        );
        const imageSrc = webview.asWebviewUri(imagePath);

        const scriptUri = webview.asWebviewUri(
            Uri.joinPath(this.extensionPath, "dist", "main-webview-provider.js")
        );

        const constantUri = webview.asWebviewUri(
            Uri.joinPath(this.extensionPath, "dist", "constant.js")
        );
        const tailwindUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "dist", "index.css"));
        const codiconsUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, 'node_modules', '@vscode', 'codicons', 'dist', 'codicon.css'));

        const nonce = Utils.getNonce();

        return `<!DOCTYPE html>
        <html>
            <head>
                <meta charset="utf-8"/>
                <meta http-equiv="Content-Security-Policy"
                        content="default-src 'none'; img-src ${webview.cspSource} https:; 
                        font-src ${webview.cspSource}; 
                        style-src ${webview.cspSource} 'unsafe-inline'; 
                        script-src 'nonce-${nonce}' 'unsafe-eval';
                        connect-src ${webview.cspSource} https:;">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${tailwindUri}" rel="stylesheet">
                <link href="${codiconsUri}" rel="stylesheet" id="vscode-codicon-stylesheet">
            </head>
            <body>
                <div id="root">
                    <img src="${imageSrc}" alt="Logo"/>
                </div>
                <script nonce="${nonce}">window.imageSrc = "${imageSrc}";</script>
                <script src="${webview.asWebviewUri(Uri.file(path.join(this.context.extensionPath, 'dist', 'bundle.js')))}"></script>
                <script nonce="${nonce}" type="text/javascript" src="${constantUri}"></script>
                <script nonce="${nonce}" src="${scriptUri}"></script>
            </body>
        </html>`;
    }

    getWebview(): Webview | undefined {
        return this.panel?.webview;
    }

    dispose() {
        this.stopStateMonitoring();
        if (this.panel) {
            this.panel.dispose();
            this.panel = undefined;
        }
    }
}