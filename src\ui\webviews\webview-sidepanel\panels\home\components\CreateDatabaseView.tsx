import * as React from 'react';
import { useState } from 'react';
import {
    VscodeButton,
    VscodeIcon,
    VscodeOption,
    VscodeSingleSelect,
    VscodeTextfield,
    VscodeRadioGroup,
    VscodeRadio,
    VscodeCollapsible,
    VscodeLabel,
    VscodeSplitLayout,
    VscodeDivider
} from '@vscode-elements/react-elements';
import VSCodeApiService from '@/services/vscode-api-service';
import "@/index.css";
import { DeployDTO } from '@/server/actions/repository-server-actions';

interface CreateDatabaseViewProps {
    onNavigateBack: () => void;
}
const defaultValues = {
    database_provider: 'PostgreSQL',
    database_version: '16',
    region: 'us',
    instance_type: 'free',
    datacenter: 'us-west-aws',
    database_username: 'postgres',
    database_password: '',
    deployment_type: 'REPOSITORY',
};

const CreateDatabaseView: React.FC<CreateDatabaseViewProps> = ({ onNavigateBack }) => {
    const [databaseName, setDatabaseName] = useState<string>('');
    const [databaseProvider, setDatabaseProvider] = useState<string>(defaultValues.database_provider);
    const [databaseVersion, setDatabaseVersion] = useState<string>(defaultValues.database_version);
    const [isCreatingDatabase, setIsCreatingDatabase] = useState<boolean>(false);
    const [cloudProvider, setCloudProvider] = useState<string>('AWS');
    const [region, setRegion] = useState<string>('USA');
    const [computeType, setComputeType] = useState<string>('free-compute');
    const [dbSystemName, setDbSystemName] = useState<string>('postgres');
    const [username, setUsername] = useState<string>('postgres');
    const [password, setPassword] = useState<string>('');

    const vscode = VSCodeApiService.getInstance();

    const handleFormSubmit = () => {
        if (!databaseName) {
            vscode.postMessage({
                command: 'alert',
                text: 'Please fill in the database name.'
            });
            return;
        }

        setIsCreatingDatabase(true);
        const deployData: DeployDTO = {
            repository_name: databaseName,
            ...defaultValues
        };

        setTimeout(() => {
            vscode.postMessage({
                action: "CREATE_REPOSITORY",
                data: { deployData }
            });

            setIsCreatingDatabase(false);
            setDatabaseName('');
            onNavigateBack();
        }, 1000);
    };

    return (
        <div className="flex flex-col h-full w-full text-[#cccccc] my-4">
            <div className="px-6">
                <div className="flex items-center gap-2 mb-4 cursor-pointer" onClick={onNavigateBack}>
                    <VscodeIcon name="chevron-left" size={16} />
                    <span className="text-sm text-[#cccccc] hover:underline">Back</span>
                </div>
            </div>
            <div className='px-6'>
                <h1 className="text-xl font-bold my-1">Create a new database</h1>
                <p className="text-sm mb-4 text-[#a0a0a0]">Fill in the details below to create your new database.</p>

                <div className="p-4 mb-4 bg-[#252525] border border-[#444444] rounded text-sm">
                    <div className='flex flex-col'>
                        <p>When you create your database, you'll get a private, dedicated instance paired with a powerful PostgreSQL database.</p>
                        <p> Enjoy your data with a new Git experience!</p>
                    </div>
                </div>
            </div>
            <div className='flex flex-col gap-2 w-full mb-4'>
                <div className='px-6'>
                    <div className="mb-4 flex flex-col gap-2">
                        <VscodeLabel className='font-semibold' required>Database Display Name </VscodeLabel>
                        <VscodeTextfield
                            placeholder="Enter your database name"
                            value={databaseName}
                            onChange={(e) => setDatabaseName((e.target as HTMLInputElement).value)}
                            className="w-full"
                        />
                    </div>

                    <div className="flex gap-4">
                        <div className="mb-4 flex-1 gap-2">
                            <VscodeLabel className='font-semibold'>Database Provider </VscodeLabel>
                            <VscodeSingleSelect
                                id="database-provider"
                                aria-label="Database Provider"
                                value={databaseProvider}
                                onChange={(e) => setDatabaseProvider((e.target as HTMLSelectElement).value)}
                                className="w-full"
                                disabled
                            >
                                <VscodeOption value="PostgreSQL" selected>PostgreSQL</VscodeOption>
                                <VscodeOption value="MySQL">MySQL</VscodeOption>
                                <VscodeOption value="SQLite">SQLite</VscodeOption>
                            </VscodeSingleSelect>
                        </div>
                        <div className="mb-4 flex-1 gap-2">
                            <VscodeLabel className='font-semibold'>Version </VscodeLabel>
                            <VscodeSingleSelect
                                id="database-version"
                                aria-label="Database Version"
                                value={databaseVersion}
                                onChange={(e) => setDatabaseVersion((e.target as HTMLSelectElement).value)}
                                className="w-full"
                                disabled
                            >
                                <VscodeOption value="16" selected>16</VscodeOption>
                                <VscodeOption value="15">15</VscodeOption>
                                <VscodeOption value="14">14</VscodeOption>
                            </VscodeSingleSelect>
                        </div>
                    </div>
                </div>

                <VscodeCollapsible title="Infrastructure" className='w-full'>
                    <div className="space-y-4 pt-2 px-6">
                        <div className="mb-4 flex flex-col gap-2">
                            <VscodeLabel className='font-semibold'>Cloud Provider </VscodeLabel>
                            <VscodeRadioGroup title='cloud-provider' onChange={(e: Event) => setCloudProvider((e as CustomEvent).detail)} >
                                <div className="flex gap-4">
                                    <VscodeRadio value="AWS" label="AWS" checked={cloudProvider === "AWS"} disabled />
                                    <VscodeRadio value="Azure" label="Azure" checked={cloudProvider === "Azure"} disabled />
                                </div>
                            </VscodeRadioGroup>
                        </div>

                        <div className="mb-4 flex flex-col gap-2">
                            <VscodeLabel className='font-semibold'>Region </VscodeLabel>
                            <VscodeSingleSelect id="region" aria-label="Region" className="w-full" disabled>
                                <VscodeOption value="USA" selected>USA</VscodeOption>
                                <VscodeOption value="Europe">Europe</VscodeOption>
                                <VscodeOption value="Asia">Asia</VscodeOption>
                            </VscodeSingleSelect>
                        </div>

                        <div className="mb-4 flex flex-col gap-2">
                            <VscodeLabel className='font-semibold'>Compute </VscodeLabel>
                            <div className="p-2 bg-[#3c3c3c] border border-[#3c3c3c] rounded text-[#cccccc] flex justify-between items-center">
                                <div className="px-1 w-full flex items-center justify-between">
                                    <div>free-compute</div>
                                    <span className="mr-4">0.1 Shared</span>
                                    <span>256 MB</span>
                                    <VscodeIcon name='chevron-down' size={14} />
                                </div>
                            </div>
                        </div>
                    </div>
                </VscodeCollapsible>

                <VscodeCollapsible title="Advanced" className='w-full'>
                    <div className="space-y-4 pt-2 px-6">
                        <div className="mb-4 flex flex-col gap-2">
                            <VscodeLabel className='font-semibold'>Database System Name </VscodeLabel>
                            <VscodeTextfield
                                placeholder="postgres"
                                value={dbSystemName}
                                onChange={(e) => setDbSystemName((e.target as HTMLInputElement).value)}
                                className="w-full"
                                disabled
                            />
                            <p className="text-xs mt-1 text-[#a0a0a0]">A unique name will help you identify this database.</p>
                        </div>

                        <div>
                            <VscodeLabel className='font-semibold'>Username </VscodeLabel>
                            <VscodeTextfield
                                placeholder="postgres"
                                value={username}
                                onChange={(e) => setUsername((e.target as HTMLInputElement).value)}
                                className="w-full"
                                disabled
                            />
                            <p className="text-xs mt-1 text-[#a0a0a0]">This will be your new login username</p>
                        </div>

                        <div>
                            <VscodeLabel className='font-semibold'>Password </VscodeLabel>
                            <VscodeTextfield
                                type="password"
                                placeholder="A secure password will be generated if not provided."
                                value={password}
                                onChange={(e) => setPassword((e.target as HTMLInputElement).value)}
                                className="w-full"
                                required
                            />
                            <p className="text-xs mt-1 text-[#a0a0a0]">Write a strong password for your database.</p>
                        </div>
                    </div>
                </VscodeCollapsible>
            </div>
            <div className="mt-4 flex justify-between px-6">
                <VscodeButton secondary onClick={onNavigateBack}>
                    <p className="text-base p-1"> Cancel</p>
                </VscodeButton>
                <VscodeButton
                    disabled={isCreatingDatabase}
                    onClick={handleFormSubmit}
                >
                    <div className="flex items-center">
                        <span className="ml-2 text-base p-1">
                            {isCreatingDatabase ? 'Creating...' : 'Create Database'}
                        </span>
                    </div>
                </VscodeButton>
            </div>
        </div>
    );
};

export default CreateDatabaseView;