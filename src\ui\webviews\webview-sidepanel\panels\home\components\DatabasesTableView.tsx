import * as React from 'react';
import { useState, useEffect, useCallback } from 'react';
import {
    VscodeButton,
    VscodeIcon,
    VscodeTextfield,
    VscodeDivider
} from '@vscode-elements/react-elements';
import VSCodeApiService from '@/services/vscode-api-service';
import "@/index.css";
import { Plus, Search, Table, ChevronRight } from 'lucide-react';
import Loader from '@/ui/webviews/webview-mainpanel/components/Loader';
import { Repositories, Repository } from '@/ui/types/repository.type';

interface DatabasesTableProps {
    onNavigateToCreate: () => void;
}

const DatabasesTable: React.FC<DatabasesTableProps> = ({ onNavigateToCreate }) => {
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [deployments, setDeployments] = useState<Repositories>([]);
    const [isLoadingDatabase, setIsLoadingDatabase] = useState<boolean>(false);
    const [selectedDatabase, setSelectedDatabase] = useState<Repository | null>(null);

    const entriesPerPage = 8;

    const vscode = VSCodeApiService.getInstance();

    const fetchAllDeployments = () => {
        try {
            setLoading(true);
            setError(null);
            vscode.postMessage({ action: "GET_ALL_REPOSITORIES", data: {} });
        } catch (err) {
            setError("Failed to fetch deployments. Please try again.");
            console.error(err);
            setLoading(false);
        }
    };

    const handleDatabaseClick = (db: Repository) => {
        try {
            setIsLoadingDatabase(true);
            setError(null);

            setSelectedDatabase(db);
            vscode.postMessage({
                action: "UPDATE_SELECTED_DATABASE",
                data: { db }
            });

            setIsLoadingDatabase(false);

        } catch (err) {
            setError("Failed to load database. Please try again.");
            setIsLoadingDatabase(false);
        }
    };

    const handleNewDatabaseClick = () => {
        onNavigateToCreate();
    };

    const filteredDatabases = searchQuery
        ? deployments.filter(db => db.repository_name.toLowerCase().includes(searchQuery.toLowerCase()))
        : deployments;

    const totalPages = Math.ceil(filteredDatabases.length / entriesPerPage);
    const startIndex = (currentPage - 1) * entriesPerPage;
    const endIndex = startIndex + entriesPerPage;
    const currentDatabases = filteredDatabases.slice(startIndex, endIndex);

    const handleNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const handlePreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const CustomIcon = ({ name, size = 16 }: { name: string, size?: number }) => {
        try {
            return <VscodeIcon name={name} size={size} />;
        } catch (error) {
            switch (name) {
                case 'add':
                    return <Plus size={size} />;
                case 'search':
                    return <Search size={size} />;
                case 'table':
                    return <Table size={size} />;
                case 'chevron-right':
                    return <ChevronRight size={size} />;
                default:
                    return null;
            }
        }
    };

    useEffect(() => {
        fetchAllDeployments();
    }, []);

    useEffect(() => {
        const messageListener = (event: MessageEvent) => {
            const message = event.data;
            switch (message.action) {
                case "UPDATE_ALL_REPOSITORIES":
                    setDeployments(message.data);
                    setLoading(false);
                    break;
                case "API_ERROR":
                    setError(message.error);
                    setLoading(false);
                    break;
                default:
                    console.warn("Unknown message action:", message.action);
            }
        };
        window.addEventListener("message", messageListener);
        return () => {
            window.removeEventListener("message", messageListener);
        };
    }, []);

    return (
        <div className="flex flex-col h-full w-full text-[#cccccc] px-6 my-4">
            <div className="mb-4 flex flex-col gap-2">
                <h1 className="text-xl font-bold">Home</h1>
                <p className="text-sm text-[#a0a0a0]">Deploy your databases in seconds 🚀 and manage them as you want</p>
            </div>
            {loading && <Loader />}
            <div className="flex gap-2 mb-8">
                <div className="flex-1">
                    <VscodeTextfield
                        placeholder="Search for a database"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery((e.target as HTMLInputElement).value)}
                        className="w-full"
                    />
                </div>
                <VscodeButton className="p-1 flex justify-center items-center">
                    <CustomIcon name="search" />
                </VscodeButton>
            </div>

            {!loading && !error && (
                <>
                    {filteredDatabases.length > 0 ? (
                        <>
                            <div className="grid grid-cols-2 gap-2 mb-4">
                                {currentDatabases.map((db) => (
                                    <div
                                        key={db.id}
                                        onClick={() => handleDatabaseClick(db)}
                                        className={`p-3 bg-[#252525] border rounded-md cursor-pointer hover:bg-[#2d2d2d] flex justify-between items-center ${selectedDatabase?.id === db.id
                                            ? "border-3 border-blue-500"
                                            : "border-[#3c3c3c]"
                                            }`}
                                    >
                                        <span className="text-sm truncate">{db.repository_name}</span>
                                        <CustomIcon name="chevron-right" />
                                    </div>
                                ))}
                            </div>

                            <div className="flex justify-center items-center gap-4 my-4">
                                <VscodeButton
                                    onClick={handlePreviousPage}
                                    disabled={currentPage === 1}
                                >
                                    Previous
                                </VscodeButton>
                                <span className="text-sm">
                                    Page {currentPage} of {totalPages}
                                </span>
                                <VscodeButton
                                    onClick={handleNextPage}
                                    disabled={currentPage === totalPages}
                                >
                                    Next
                                </VscodeButton>
                            </div>
                        </>
                    ) : (
                        <Loader />)
                    }
                </>
            )}

            <div className="flex justify-center mx-auto mt-4">
                <VscodeButton
                    onClick={handleNewDatabaseClick}
                    className="flex items-center py-1 px-3"
                >
                    <CustomIcon name="add" />
                    <span className="ml-2">New database</span>
                </VscodeButton>
            </div>
        </div >
    );
};

export default DatabasesTable;