import * as React from 'react';
import { useState } from 'react';
import DatabasesTable from './DatabasesTableView';
import CreateDatabaseView from './CreateDatabaseView';

const HomeView: React.FC = () => {
    const [currentView, setCurrentView] = useState<'table' | 'create'>('table');

    return (
        <div className="h-full w-full">
            {currentView === 'table' ? (
                <DatabasesTable onNavigateToCreate={() => setCurrentView('create')} />
            ) : (
                <CreateDatabaseView onNavigateBack={() => setCurrentView('table')} />
            )}
        </div>
    );
};

export default HomeView;