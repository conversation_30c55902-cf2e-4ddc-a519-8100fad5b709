import * as vscode from 'vscode';
import { WebviewViewProvider, WebviewView, Webview, Uri, window, EventEmitter, ExtensionContext } from "vscode";
import { Utils } from "@/ui/utils/utils";
import { createRepository, DeployDTO, getRepositories, getRepository } from "@/server/actions/repository-server-actions";
import { Deployment } from "@/ui/types/instant-db/deployment.type";
import { Repository } from '@/ui/types/repository.type';

type WebviewMessage =
    | { action: "SHOW_WARNING_LOG"; data: { message: string } }
    | { action: "GET_ALL_REPOSITORIES"; data: {} }
    | { action: "CREATE_REPOSITORY"; data: { deployData: DeployDTO } }
    | { action: "GET_REPOSITORY"; data: { deploymentId: string } }
    | { action: "UPDATE_SELECTED_DATABASE"; data: { db: Repository } }
    | { action: "API_ERROR"; error: string };

export class HomeSidePanelWebview implements WebviewViewProvider {
    private _view: WebviewView | undefined;
    private _context: vscode.ExtensionContext;
    private messageHandlers: Map<string, (message: any, webview: Webview) => void> = new Map();
    private data: any[] = [];
    private onDidChangeTreeData: EventEmitter<any | undefined | null | void> = new EventEmitter();

    constructor(private readonly extensionPath: Uri, private readonly context: ExtensionContext) {
        this._context = context;
        this.registerDefaultHandlers();
    }

    registerMessageHandler(action: string, handler: (message: any, webview: Webview) => void) {
        this.messageHandlers.set(action, handler);
    }

    private registerDefaultHandlers() {
        this.registerMessageHandler("GET_ALL_REPOSITORIES", async (message, webview) => {
            try {
                const repositories = await getRepositories();
                webview.postMessage({ action: "UPDATE_ALL_REPOSITORIES", data: repositories });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET repositories: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("GET_REPOSITORY", async (message, webview) => {
            try {
                const repository = await getRepository(message.data.deploymentId);
                webview.postMessage({ action: "UPDATE_REPOSITORY", data: repository });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to GET repository: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });

        this.registerMessageHandler("CREATE_REPOSITORY", async (message, webview) => {
            try {
                const repository = await createRepository(message.data.deployData);
                console.log("Created repo:", repository);
                this._context.globalState.update('selectedRepoData', repository);
                const repositories = await getRepositories();
                webview.postMessage({ action: "UPDATE_ALL_REPOSITORIES", data: repositories });
            } catch (error) {
                webview.postMessage({
                    action: "API_ERROR",
                    error: `Failed to create repository: ${error instanceof Error ? error.message : String(error)}`,
                });
            }
        });
    }

    refresh(): void {
        this.onDidChangeTreeData.fire(null);
        if (this._view?.webview) {
            this._view.webview.postMessage({
                action: 'UPDATE_ALL_REPOSITORIES',
            });
        }
    }

    resolveWebviewView(webviewView: WebviewView): void {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.extensionPath],
        };
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
        this._activateMessageListener();
    }

    private _activateMessageListener() {
        if (!this._view?.webview) { return; }

        const currentPanel = this._view;

        currentPanel.webview.onDidReceiveMessage((message: WebviewMessage) => {
            switch (message.action) {
                case 'SHOW_WARNING_LOG':
                    window.showWarningMessage(message.data.message);
                    break;
                case 'GET_ALL_REPOSITORIES':
                    if (currentPanel) {
                        currentPanel.webview.postMessage({
                            action: 'UPDATE_ALL_REPOSITORIES',
                            data: this.data
                        });
                    }
                    break;
                case 'GET_REPOSITORY':
                    if (currentPanel) {
                        currentPanel.webview.postMessage({
                            action: 'UPDATE_REPOSITORY',
                            data: this.data
                        });
                    }
                    break;
                case "UPDATE_SELECTED_DATABASE":
                    const db = message.data.db;
                    this._context.globalState.update('selectedRepoData', db);
                    break;
                case 'CREATE_REPOSITORY':
                    if (currentPanel) {
                        currentPanel.webview.postMessage({
                            action: 'UPDATE_ALL_REPOSITORIES',
                            data: this.data
                        });
                    }
                    break;
            }
            const handler = this.messageHandlers.get(message.action);

            if (handler) {
                handler(message, this._view!.webview);
            } else if (!['SHOW_WARNING_LOG', 'GET_ALL_REPOSITORIES', 'GET_REPOSITORY', 'UPDATE_SELECTED_DATABASE', 'CREATE_REPOSITORY'].includes(message.action)) {
                console.warn(`(Side Panel) No handler for action: ${message.action}`);
            }
        });

        this._view.onDidDispose(() => {
            this._view = undefined;
            this.messageHandlers.clear();
        }, null);
    }

    private _getHtmlForWebview(webview: Webview) {
        const scriptUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "dist", "side-panel-home-webview-provider.js"));
        const constantUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "dist", "constant.js"));
        const tailwindUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "dist", "index.css"));
        const codiconsUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "node_modules", "@vscode/codicons", "dist", "codicon.css"));
        const nonce = Utils.getNonce();

        return `<!DOCTYPE html>
            <html>
                <head>
                    <meta charset="utf-8"/>
                    <meta http-equiv="Content-Security-Policy"
                          content="default-src 'none'; img-src vscode-resource: https:; 
                          font-src ${webview.cspSource}; 
                          style-src ${webview.cspSource} 'unsafe-inline'; 
                          script-src 'nonce-${nonce}' 'unsafe-eval';
                          connect-src ${webview.cspSource} https:;">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <link href="${tailwindUri}" rel="stylesheet">
                    <link href="${codiconsUri}" rel="stylesheet" id="vscode-codicon-stylesheet">
                </head>
                <body>
                    <div id="root"></div>
                    <script nonce="${nonce}">
                        window.addEventListener('message', event => {
                        });
                        window.addEventListener('load', () => {
                        });
                    </script>
                    <script nonce="${nonce}" type="text/javascript" src="${constantUri}"></script>
                    <script nonce="${nonce}" src="${scriptUri}"></script>
                </body>
            </html>`;
    }
}
