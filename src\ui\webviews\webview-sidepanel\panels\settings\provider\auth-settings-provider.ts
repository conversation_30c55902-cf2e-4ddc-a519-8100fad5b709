import * as vscode from "vscode";
import * as fs from "fs";
import * as path from "path";
import { exec } from "child_process";
import * as os from "os";

export class AuthSettingsProvider implements vscode.TreeDataProvider<AuthSettingItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<AuthSettingItem | undefined | void> =
    new vscode.EventEmitter<AuthSettingItem | undefined | void>();
  readonly onDidChangeTreeData: vscode.Event<AuthSettingItem | undefined | void> =
    this._onDidChangeTreeData.event;

  private settings: { key: string; value: string }[] = [];

  constructor(private context: vscode.ExtensionContext) {
    this.loadSettings();
  }

  refresh(): void {
    this.loadSettings();
    this._onDidChangeTreeData.fire();
  }

  getTreeItem(element: AuthSettingItem): vscode.TreeItem {
    return element;
  }

  getChildren(element?: AuthSettingItem): Thenable<AuthSettingItem[]> {
    if (!element) {
      return Promise.resolve(
        this.settings.map(
          (setting) => new AuthSettingItem(setting.key, setting.value, this, false)
        )
      );
    } else if (!element.isButton) {
      return Promise.resolve([
        new AuthSettingItem(element.key, "Check Value", this, true),
      ]);
    }
    return Promise.resolve([]);
  }

  private loadSettings() {
    const envPath = path.join(this.context.extensionPath, ".env");
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, "utf-8");
      this.settings = envContent
        .split("\n")
        .filter((line) => line.includes("="))
        .map((line) => {
          const [key, value] = line.split("=");
          return { key: key.trim(), value: value.trim() };
        });
    }
  }

  updateSetting(key: string, newValue: string) {
    const envPath = path.join(this.context.extensionPath, ".env");

    if (fs.existsSync(envPath)) {
      let envContent = fs.readFileSync(envPath, "utf-8");
      envContent = envContent.replace(new RegExp(`${key}=.*`), `${key}=${newValue}`);
      fs.writeFileSync(envPath, envContent);
    } else {
      fs.writeFileSync(envPath, `${key}=${newValue}\n`);
    }

    const platform = os.platform();

    if (platform === "win32") {
      // Windows
      exec(`set ${key}=${newValue}`, (err) => {
        if (err) {
          vscode.window.showErrorMessage(`Failed to update ${key} in this terminal.`);
        } else {
          vscode.window.showInformationMessage(`${key} updated!`);
        }
      });
      exec(`setx ${key} "${newValue}"`, (err) => {
        if (err) {
          vscode.window.showErrorMessage(`Failed to update ${key}. Try running VS Code as admin.`);
        } else {
          vscode.window.showInformationMessage(`${key} updated! Reload VS Code to apply changes.`);
        }
      });
    } else {
      // macOS / Linux
      const shellProfile = this.getShellProfile();
      const exportCommand = `export ${key}="${newValue}"`;

      if (shellProfile) {
        fs.appendFileSync(shellProfile, `\n${exportCommand}`);
      }

      exec(exportCommand, (err) => {
        if (err) {
          vscode.window.showErrorMessage(`Failed to update ${key} in environment.`);
        } else {
          vscode.window.showInformationMessage(
            `${key} updated! Restart your terminal or run 'source ${shellProfile}' to apply changes.`
          );
        }
      });
    }

    this.refresh();
  }

  checkRegisteredValue(key: string) {
    exec(`node -e "console.log('${key}:', process.env.${key});"`, (err, stdout) => {
      if (err) {
        vscode.window.showErrorMessage(`Failed to fetch value for ${key}.`);
      } else {
        vscode.window.showInformationMessage(stdout.trim());
      }
    });
  }

  private getShellProfile(): string | null {
    const homeDir = os.homedir();
    const shell = process.env.SHELL || "";

    if (shell.includes("zsh")) {
      return path.join(homeDir, ".zshrc");
    } else if (shell.includes("bash")) {
      return path.join(homeDir, ".bashrc");
    } else {
      return path.join(homeDir, ".profile");
    }
  }
}

class AuthSettingItem extends vscode.TreeItem {
  constructor(
    public readonly key: string,
    public readonly value: string,
    private provider: AuthSettingsProvider,
    public readonly isButton: boolean
  ) {
    super(
      isButton ? "Check Value" : `${key}: ${value}`,
      isButton ? vscode.TreeItemCollapsibleState.None : vscode.TreeItemCollapsibleState.Collapsed
    );

    this.contextValue = isButton ? "checkValueButton" : "authSetting";

    if (isButton) {
      this.command = {
        command: "authSettings.checkSetting",
        title: "Check Value",
        arguments: [this],
      };
      this.iconPath = new vscode.ThemeIcon("eye");
    } else {
      this.command = {
        command: "authSettings.editSetting",
        title: "Edit",
        arguments: [this],
      };
    }
  }
}