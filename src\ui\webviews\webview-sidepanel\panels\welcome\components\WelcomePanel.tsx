import * as React from 'react';
import { useState, useEffect } from 'react';
import SnapshotGraph from '@/ui/components/SnapshotGraph';
import { Snapshot } from '@/ui/types/instant-db/snapshot.type';
import "@/index.css";
import VSCodeApiService from '@/services/vscode-api-service';
import { VscodeButton } from '@vscode-elements/react-elements';

interface WalkthroughProgress {
    doneCount: number;
    allCount: number;
    progress: number;
}

const WelcomePanel: React.FC = () => {

    const [showWalkthrough, setShowWalkthrough] = useState(true);
    const [showWelcome, setShowWelcome] = useState(true);
    const [showCreateRepo, setShowCreateRepo] = useState(true);
    const [walkthroughProgress, setWalkthroughProgress] = useState<WalkthroughProgress>({
        doneCount: 0,
        allCount: 10,
        progress: 0
    });
    const vscode = VSCodeApiService.getInstance();

    const handleButtonClick = (action: string) => {
        vscode.postMessage({
            action: 'BUTTON_CLICKED',
            data: { action }
        });
    };

    const dismissWalkthrough = () => {
        vscode.postMessage({
            action: 'DISMISS_WALKTHROUGH',
            data: {}
        });
        setWalkthroughProgress({
            doneCount: 0,
            allCount: 0,
            progress: 0
        });
    };

    const openWalkthrough = () => {
        vscode.postMessage({
            action: 'OPEN_WALKTHROUGH',
            data: {
                source: {
                    source: 'home',
                    detail: 'onboarding'
                }
            }
        });
    };

    return (
        <div className="flex flex-col h-full w-full text-[#cccccc] text-sm">
            {/* Community Section */}
            <div className="flex justify-between items-center px-1 py-2 mb-2">
                <div className="flex items-center">
                    <span className="codicon codicon-organization mr-2"></span>
                    <span className="uppercase text-xs font-medium">Community</span>
                </div>
                <div className="text-xs uppercase font-medium text-[#888888]">Connect</div>
            </div>

            {walkthroughProgress && (
                <div className="mx-2 mb-2 relative">
                    <div className="p-2 relative ">


                        <div
                            className="cursor-pointer"
                            onClick={openWalkthrough}
                            title="Open Walkthrough"
                        >

                            <div className="flex items-center justify-between mb-1">
                                <span className="text-xs">
                                    Guepard Walkthrough ({walkthroughProgress.doneCount}/{walkthroughProgress.allCount})
                                </span>
                                <button
                                    onClick={dismissWalkthrough}
                                    className="hover:bg-[#3a3a3a] rounded"
                                    title="Dismiss"
                                    aria-label="Dismiss"
                                >
                                    <span className="codicon codicon-close text-xs"></span>
                                </button>
                            </div>

                            <div className="w-full bg-[#333333] h-1 rounded overflow-hidden">
                                <div
                                    className="bg-[#0e639c] h-full"
                                    style={{
                                        width: `${(walkthroughProgress.doneCount / walkthroughProgress.allCount) * 100}%`,
                                    }}
                                ></div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Welcome Section */}
            {showWelcome && (
                <div className="mx-2 mb-2 bg-[#252526] border border-[#444444] rounded relative">
                    <div className="p-3 text-xs">
                        <div className="flex justify-between">
                            <span className="font-medium">Welcome to Guepard Home View!</span>
                            <button onClick={() => setShowWelcome(false)} className="hover:text-white" title="Close welcome message">
                                <span className="codicon codicon-close"></span>
                            </button>
                        </div>
                        <p className="mt-2 text-[#bbbbbb]">
                            This is a hub for your current, future, and recent work.
                            We're continuing to refine this experience and welcome
                            your feedback
                        </p>
                    </div>
                </div>
            )}

            {/* Create Repository Section */}
            {showCreateRepo && (
                <div className="mx-2 mb-2 bg-[#252526] border border-[#444444] rounded relative">
                    <div className="p-3 text-xs">
                        <div className="flex justify-between">
                            <span className="font-medium">Create your first repository</span>
                            <button onClick={() => setShowCreateRepo(false)} className="hover:text-white" title="Close repository creation">
                                <span className="codicon codicon-close"></span>
                            </button>
                        </div>
                        <p className="mt-2 text-[#bbbbbb]">
                            Connect, manage, and organize managed databases in
                            Guepard to streamline data version control and
                            centralized management.
                        </p>
                        <div className="mt-3 flex justify-center">
                            <VscodeButton>
                                <div
                                    onClick={() => handleButtonClick('NEW_DATABASE')}
                                >
                                    New database
                                </div>
                            </VscodeButton>
                        </div>
                    </div>
                </div>
            )}

            {/* Popular Views Section */}
            <div className="px-1 py-2">
                <div className="uppercase text-xs font-medium text-[#888888] mb-2">Popular Views</div>

                <div className="space-y-1">
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-graph mr-2"></span>
                        <span>Database history graph</span>
                    </div>
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-bookmark mr-2"></span>
                        <span>Bookmark details</span>
                    </div>
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-git-branch mr-2"></span>
                        <span>Branches</span>
                    </div>
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-pulse mr-2"></span>
                        <span>Monitoring</span>
                    </div>
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-list-flat mr-2"></span>
                        <span>Logs</span>
                    </div>
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-settings-gear mr-2"></span>
                        <span>Settings</span>
                    </div>
                </div>
            </div>

            {/* Getting Started Section */}
            <div className="px-1 py-2 mt-4">
                <div className="uppercase text-xs font-medium text-[#888888] mb-2">Getting Started</div>

                <div className="space-y-1">
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-checkbox mr-2"></span>
                        <span>Welcome</span>
                    </div>
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-info mr-2"></span>
                        <span>Feature Walkthrough</span>
                    </div>
                    <div className="flex items-center p-1 hover:bg-[#2a2d2e] rounded cursor-pointer">
                        <span className="codicon codicon-play mr-2"></span>
                        <span>Watch Tutorial Video</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WelcomePanel;