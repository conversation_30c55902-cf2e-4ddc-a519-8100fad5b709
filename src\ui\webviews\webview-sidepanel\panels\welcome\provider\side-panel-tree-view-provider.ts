import * as vscode from "vscode";

export class TreeViewProvider implements vscode.TreeDataProvider<HomeItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<HomeItem | undefined | void> = new vscode.EventEmitter<HomeItem | undefined | void>();
  readonly onDidChangeTreeData: vscode.Event<HomeItem | undefined | void> = this._onDidChangeTreeData.event;

  private items: HomeItem[];

  constructor(items: HomeItem[]) {
    this.items = items;
  }

  getTreeItem(element: HomeItem): vscode.TreeItem {
    return element;
  }

  getChildren(): HomeItem[] {
    return this.items;
  }

  refresh(): void {
    this._onDidChangeTreeData.fire();
  }
}

export class HomeItem extends vscode.TreeItem {
  constructor(
    label: string,
    collapsibleState: vscode.TreeItemCollapsibleState = vscode.TreeItemCollapsibleState.None,
    command?: vscode.Command,
    children: HomeItem[] = []
  ) {
    super(label, children.length ? vscode.TreeItemCollapsibleState.Collapsed : collapsibleState);
    this.command = command;
    this.children = children;
  }

  readonly children: HomeItem[];
}
