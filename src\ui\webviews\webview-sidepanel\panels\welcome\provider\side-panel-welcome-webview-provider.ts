import { WebviewViewProvider, WebviewView, Webview, Uri, window } from "vscode";
import { Utils } from "@/ui/utils/utils";

type WebviewMessage = { action: "SHOW_WARNING_LOG"; data: { message: string } };

export class WelcomeSidePanelWebview implements WebviewViewProvider {
    private _view: WebviewView | null = null;

    constructor(private readonly extensionPath: Uri) { }

    resolveWebviewView(webviewView: WebviewView): void {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.extensionPath],
        };
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
        this._activateMessageListener();
    }

    private _activateMessageListener() {
        this._view?.webview.onDidReceiveMessage((message: WebviewMessage) => {
            if (message.action === "SHOW_WARNING_LOG") {
                window.showWarningMessage(message.data.message);
            }
        });
    }

    private _getHtmlForWebview(webview: Webview) {
        const scriptUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "dist", "side-panel-welcome-webview-provider.js"));
        const constantUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "dist", "constant.js"));
        const tailwindUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "dist", "index.css"));
        const codiconsUri = webview.asWebviewUri(Uri.joinPath(this.extensionPath, "node_modules", "@vscode/codicons", "dist", "codicon.css"));
        const nonce = Utils.getNonce();

        return `<!DOCTYPE html>
            <html>
                <head>
                    <meta charset="utf-8"/>
                    <meta http-equiv="Content-Security-Policy"
                          content="default-src 'none'; img-src vscode-resource: https:; 
                          font-src ${webview.cspSource}; 
                          style-src ${webview.cspSource} 'unsafe-inline'; 
                          script-src 'nonce-${nonce}' 'unsafe-eval';
                          connect-src ${webview.cspSource} https:;">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <link href="${tailwindUri}" rel="stylesheet">
                    <link href="${codiconsUri}" rel="stylesheet" id="vscode-codicon-stylesheet">
                </head>
                <body>
                    <div id="root"></div>
                    <script nonce="${nonce}">
                        window.addEventListener('message', event => {
                        });
                        window.addEventListener('load', () => {
                        });
                    </script>
                    <script nonce="${nonce}" type="text/javascript" src="${constantUri}"></script>
                    <script nonce="${nonce}" src="${scriptUri}"></script>
                </body>
            </html>`;
    }
}
