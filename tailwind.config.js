/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{tsx,ts,jsx,js}"],
  theme: {
    extend: {
      colors: {
        vscode: {
          background: 'var(--vscode-editor-background, #1e1e1e)',
          foreground: 'var(--vscode-editor-foreground, #d4d4d4)',
          widgetBackground: 'var(--vscode-widget-background, #252526)',
          widgetBorder: 'var(--vscode-widget-border, #454545)',
          activityBar: {
            background: 'var(--vscode-activityBar-background, #333333)',
            foreground: 'var(--vscode-activityBar-foreground, #ffffff)',
            inactiveForeground: 'var(--vscode-activityBar-inactiveForeground, #ffffff66)',
            border: 'var(--vscode-activityBar-border, #00000000)',
          },
          sideBar: {
            background: 'var(--vscode-sideBar-background, #252526)',
            foreground: 'var(--vscode-sideBar-foreground, #cccccc)',
            border: 'var(--vscode-sideBar-border, #00000000)',
          },
          panel: {
            background: 'var(--vscode-panel-background, #1e1e1e)',
            border: 'var(--vscode-panel-border, #80808033)',
          },
          statusBar: {
            background: 'var(--vscode-statusBar-background, #007acc)',
            foreground: 'var(--vscode-statusBar-foreground, #ffffff)',
            noFolderBackground: 'var(--vscode-statusBar-noFolderBackground, #68217a)',
          },
          titleBar: {
            activeBackground: 'var(--vscode-titleBar-activeBackground, #3c3c3c)',
            activeForeground: 'var(--vscode-titleBar-activeForeground, #cccccc)',
            inactiveBackground: 'var(--vscode-titleBar-inactiveBackground, #2d2d2d)',
            inactiveForeground: 'var(--vscode-titleBar-inactiveForeground, #999999)',
          },
          button: {
            background: 'var(--vscode-button-background, #0e639c)',
            foreground: 'var(--vscode-button-foreground, #ffffff)',
            hoverBackground: 'var(--vscode-button-hoverBackground, #1177bb)',
            secondaryBackground: 'var(--vscode-button-secondaryBackground, #3a3d41)',
            secondaryForeground: 'var(--vscode-button-secondaryForeground, #ffffff)',
            secondaryHoverBackground: 'var(--vscode-button-secondaryHoverBackground, #4c5055)',
          },
          input: {
            background: 'var(--vscode-input-background, #3c3c3c)',
            foreground: 'var(--vscode-input-foreground, #cccccc)',
            border: 'var(--vscode-input-border, #00000000)',
            placeholderForeground: 'var(--vscode-input-placeholderForeground, #cccccc7e)',
          },
          list: {
            hoverBackground: 'var(--vscode-list-hoverBackground, #2a2d2e)',
            hoverForeground: 'var(--vscode-list-hoverForeground, #cccccc)',
            activeSelectionBackground: 'var(--vscode-list-activeSelectionBackground, #094771)',
            activeSelectionForeground: 'var(--vscode-list-activeSelectionForeground, #ffffff)',
            inactiveSelectionBackground: 'var(--vscode-list-inactiveSelectionBackground, #37373d)',
            focusBackground: 'var(--vscode-list-focusBackground, #062f4a)',
            highlightForeground: 'var(--vscode-list-highlightForeground, #18a3ff)',
          },
          tab: {
            activeBackground: 'var(--vscode-tab-activeBackground, #1e1e1e)',
            activeForeground: 'var(--vscode-tab-activeForeground, #ffffff)',
            inactiveBackground: 'var(--vscode-tab-inactiveBackground, #2d2d2d)',
            inactiveForeground: 'var(--vscode-tab-inactiveForeground, #ffffff80)',
            border: 'var(--vscode-tab-border, #252525)',
            activeBorder: 'var(--vscode-tab-activeBorder, #ffffff)',
          },
          accent: {
            primary: 'var(--vscode-focusBorder, #007fd4)',
            secondary: 'var(--vscode-progressBar-background, #0e70c0)',
          },
          text: {
            link: 'var(--vscode-textLink-foreground, #3794ff)',
            linkActive: 'var(--vscode-textLink-activeForeground, #4dabf7)',
            preformatted: 'var(--vscode-textPreformat-foreground, #d7ba7d)',
            error: 'var(--vscode-errorForeground, #f48771)',
          },
          contrast: {
            activeBorder: 'var(--vscode-contrastActiveBorder, #f38518)',
            border: 'var(--vscode-contrastBorder, #6fc3df)',
          },
        }
      }
    },
  },
  plugins: [
    function({ addBase, theme }) {
      const vscodeColors = {
        '--vscode-editor-background': theme('colors.vscode.background'),
        '--vscode-editor-foreground': theme('colors.vscode.foreground'),
        '--vscode-widget-background': theme('colors.vscode.widgetBackground'),
        '--vscode-widget-border': theme('colors.vscode.widgetBorder'),
        '--vscode-activityBar-background': theme('colors.vscode.activityBar.background'),
        '--vscode-activityBar-foreground': theme('colors.vscode.activityBar.foreground'),
        '--vscode-activityBar-inactiveForeground': theme('colors.vscode.activityBar.inactiveForeground'),
        '--vscode-activityBar-border': theme('colors.vscode.activityBar.border'),
        '--vscode-sideBar-background': theme('colors.vscode.sideBar.background'),
        '--vscode-sideBar-foreground': theme('colors.vscode.sideBar.foreground'),
        '--vscode-sideBar-border': theme('colors.vscode.sideBar.border'),
        '--vscode-panel-background': theme('colors.vscode.panel.background'),
        '--vscode-panel-border': theme('colors.vscode.panel.border'),
        '--vscode-statusBar-background': theme('colors.vscode.statusBar.background'),
        '--vscode-statusBar-foreground': theme('colors.vscode.statusBar.foreground'),
        '--vscode-statusBar-noFolderBackground': theme('colors.vscode.statusBar.noFolderBackground'),
        '--vscode-titleBar-activeBackground': theme('colors.vscode.titleBar.activeBackground'),
        '--vscode-titleBar-activeForeground': theme('colors.vscode.titleBar.activeForeground'),
        '--vscode-titleBar-inactiveBackground': theme('colors.vscode.titleBar.inactiveBackground'),
        '--vscode-titleBar-inactiveForeground': theme('colors.vscode.titleBar.inactiveForeground'),
        '--vscode-button-background': theme('colors.vscode.button.background'),
        '--vscode-button-foreground': theme('colors.vscode.button.foreground'),
        '--vscode-button-hoverBackground': theme('colors.vscode.button.hoverBackground'),
        '--vscode-button-secondaryBackground': theme('colors.vscode.button.secondaryBackground'),
        '--vscode-button-secondaryForeground': theme('colors.vscode.button.secondaryForeground'),
        '--vscode-button-secondaryHoverBackground': theme('colors.vscode.button.secondaryHoverBackground'),
        '--vscode-input-background': theme('colors.vscode.input.background'),
        '--vscode-input-foreground': theme('colors.vscode.input.foreground'),
        '--vscode-input-border': theme('colors.vscode.input.border'),
        '--vscode-input-placeholderForeground': theme('colors.vscode.input.placeholderForeground'),
        '--vscode-list-hoverBackground': theme('colors.vscode.list.hoverBackground'),
        '--vscode-list-hoverForeground': theme('colors.vscode.list.hoverForeground'),
        '--vscode-list-activeSelectionBackground': theme('colors.vscode.list.activeSelectionBackground'),
        '--vscode-list-activeSelectionForeground': theme('colors.vscode.list.activeSelectionForeground'),
        '--vscode-list-inactiveSelectionBackground': theme('colors.vscode.list.inactiveSelectionBackground'),
        '--vscode-list-focusBackground': theme('colors.vscode.list.focusBackground'),
        '--vscode-list-highlightForeground': theme('colors.vscode.list.highlightForeground'),
        '--vscode-tab-activeBackground': theme('colors.vscode.tab.activeBackground'),
        '--vscode-tab-activeForeground': theme('colors.vscode.tab.activeForeground'),
        '--vscode-tab-inactiveBackground': theme('colors.vscode.tab.inactiveBackground'),
        '--vscode-tab-inactiveForeground': theme('colors.vscode.tab.inactiveForeground'),
        '--vscode-tab-border': theme('colors.vscode.tab.border'),
        '--vscode-tab-activeBorder': theme('colors.vscode.tab.activeBorder'),
        '--vscode-focusBorder': theme('colors.vscode.accent.primary'),
        '--vscode-progressBar-background': theme('colors.vscode.accent.secondary'),
        '--vscode-textLink-foreground': theme('colors.vscode.text.link'),
        '--vscode-textLink-activeForeground': theme('colors.vscode.text.linkActive'),
        '--vscode-textPreformat-foreground': theme('colors.vscode.text.preformatted'),
        '--vscode-errorForeground': theme('colors.vscode.text.error'),
        '--vscode-contrastActiveBorder': theme('colors.vscode.contrast.activeBorder'),
        '--vscode-contrastBorder': theme('colors.vscode.contrast.border'),
      };

      addBase({
        ':root': vscodeColors,
      });
    }
  ],
};
