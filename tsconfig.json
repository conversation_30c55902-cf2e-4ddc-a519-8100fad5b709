{
	"compilerOptions": {
		"typeRoots": [
			"node_modules/@types"
		],
		"module": "commonjs", // Keep commonjs for VS Code compatibility
		"target": "ES2020", // Target modern JavaScript features
		"outDir": "dist", // Output compiled files to dist/
		"lib": [
			"ES2020",
			"DOM"
		], // Include ES2020 and DOM library for extension APIs
		"sourceMap": true, // Enable source maps for debugging
		"rootDir": "src", // Root directory of your source code
		"strict": true, // Enable strict type-checking options
		"jsx": "react-jsx", // React JSX support if using React
		"esModuleInterop": true, // Allows importing ES6 modules in CJS
		"removeComments": true, // Remove comments in compiled JS
		"skipLibCheck": true, // Skip type checking of declaration files
		"forceConsistentCasingInFileNames": true, // Ensure consistent casing in file names
		"allowUmdGlobalAccess": true, // Allow access to UMD globals (if needed)
		"baseUrl": "./src", // Base URL for module resolution
		"paths": {
			"@/*": [
				"./*"
			]
		}
	},
	"include": [
		"src/**/*",
		"src/scripts/auth-setup.js",
		"environment.d.ts"
	],
	"exclude": [
		"node_modules/**",
		".vscode-test",
		"src/auth"
	]
}